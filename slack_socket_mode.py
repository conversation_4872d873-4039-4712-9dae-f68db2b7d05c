import logging
from typing import Optional

import psycopg
from slack_sdk.socket_mode import <PERSON><PERSON><PERSON>ode<PERSON><PERSON>
from slack_sdk.socket_mode.request import SocketModeRequest
from slack_sdk.socket_mode.response import SocketModeResponse
from slack_sdk import WebClient

from config import SLACK_TOKEN, SLACK_APP_TOKEN, ALEXBOT_DB_URL, PORTAL_DB_URL
from utils import get_db_connection, get_robot_by_channel
from visualization_requests import request_visualization
from info_command import handle_info_command_logic

logger = logging.getLogger(__name__)


def handle_viz_command(
    slack_client: WebClient,
    alexbot_conn: psycopg.Connection,
    channel_name: str,
    user_name: str,
    robot_id: Optional[str],
) -> str:
    """Handle /viz command. Returns response message."""
    try:
        # Determine robot ID
        if robot_id:
            # Robot was specified as argument
            logger.info(f"User @{user_name} requested visualization for robot {robot_id} in channel {channel_name}")
        else:
            # No robot specified, determine from channel
            robot_id = get_robot_by_channel(alexbot_conn, channel_name)
            if not robot_id:
                return (
                    f"❌ Error: No robot configured for channel {channel_name}. Please specify a robot: `/viz <robot>`"
                )
            logger.info(f"User @{user_name} requested visualization for channel {channel_name} (robot: {robot_id})")

        # Request visualization (bypass rate limit for /viz commands)
        success = request_visualization(alexbot_conn, robot_id, channel_name, bypass_rate_limit=True)

        if success:
            return f"✅ Visualization requested for robot `{robot_id}`. You'll receive it in this channel shortly."
        else:
            return f"❌ Visualization request for robot `{robot_id}` failed. Please try again later."
    except Exception as e:
        logger.error(f"Error handling /viz command: {e}")
        return "❌ Error: Failed to process visualization request"


def handle_info_command(
    slack_client: WebClient,
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    channel_name: str,
    user_name: str,
    robot_serial: str,
) -> str:
    """Handle /info command. Returns response message for private reply or posts to channel."""
    try:
        # Use the extracted business logic
        success, message, _ = handle_info_command_logic(
            alexbot_conn, portal_conn, channel_name, user_name, robot_serial
        )

        if success:
            # Post successful result to channel
            _post_message_to_channel(slack_client, channel_name, message)
            return ""  # Empty response for private reply
        else:
            # Return error message for private reply
            return message

    except Exception as e:
        logger.error(f"Error handling /info command: {e}")
        return "❌ Error: Failed to process robot info request"


def _post_message_to_channel(slack_client: WebClient, channel_name: str, message: str) -> bool:
    """Helper function to post a message to a Slack channel."""
    try:
        # Lookup channel ID by channel name
        try:
            # Get list of all channels
            channels_response = slack_client.conversations_list(types="public_channel", limit=1000)
            if not channels_response["ok"]:
                logger.error(f"Failed to get channels list: {channels_response}")
                return False

            channel_id = None
            for ch in channels_response["channels"]:
                if ch["name"] == channel_name.lstrip("#"):
                    channel_id = ch["id"]
                    logger.info(f"Found channel ID for {channel_name}: {channel_id}")
                    break

            if not channel_id:
                logger.error(f"Channel {channel_name} not found")
                return False

        except Exception as e:
            logger.error(f"Error looking up channel {channel_name}: {e}")
            return False

        # Post message to channel
        response = slack_client.chat_postMessage(channel=channel_id, text=message)

        if response["ok"]:
            logger.info(f"Successfully posted message to channel {channel_name} (ID: {channel_id})")
            return True
        else:
            logger.error(f"Failed to post message to channel: {response}")
            return False

    except Exception as e:
        logger.error(f"Failed to post message to channel {channel_name}: {e}")
        return False


def handle_socket_mode_request(client: SocketModeClient, req: SocketModeRequest):
    """Handle incoming Socket Mode requests."""
    try:
        # Check if this is a slash command
        if req.type == "slash_commands":
            payload = req.payload

            # Check if this is the /viz command
            if payload.get("command") == "/viz":
                # Get necessary information
                channel_name = f"#{payload['channel_name']}"
                user_name = payload["user_name"]
                robot_id = payload.get("text", "")

                logger.info(f"Received /viz command from user @{user_name} in channel {channel_name}: {robot_id}")

                # Connect to database
                alexbot_conn = get_db_connection(ALEXBOT_DB_URL)

                try:
                    # Handle the command
                    response_text = handle_viz_command(
                        client.web_client, alexbot_conn, channel_name, user_name, robot_id
                    )

                    # Send response
                    response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": response_text})
                    client.send_socket_mode_response(response)

                finally:
                    alexbot_conn.close()

            elif payload.get("command") == "/info":
                # Get necessary information
                channel_name = f"#{payload['channel_name']}"
                user_name = payload["user_name"]
                robot_serial = payload.get("text", "").strip()

                logger.info(f"Received /info command from user @{user_name} in channel {channel_name}: {robot_serial}")

                # Connect to databases
                alexbot_conn = get_db_connection(ALEXBOT_DB_URL)
                portal_conn = get_db_connection(PORTAL_DB_URL)

                try:
                    # Handle the command
                    response_text = handle_info_command(
                        client.web_client, alexbot_conn, portal_conn, channel_name, user_name, robot_serial
                    )

                    # Send response (empty payload if posted to channel, error message if private)
                    if response_text:
                        # Error case - send private error message
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": response_text})
                    else:
                        # Success case - send empty acknowledgment (message already posted to channel)
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={})
                    client.send_socket_mode_response(response)

                finally:
                    alexbot_conn.close()
                    portal_conn.close()

            else:
                # Unknown command
                response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": "❌ Unknown command"})
                client.send_socket_mode_response(response)
        else:
            # Not a slash command
            response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": "❌ Invalid request type"})
            client.send_socket_mode_response(response)

    except Exception as e:
        logger.error(f"Error handling Socket Mode request: {e}")
        response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": "❌ Internal server error"})
        client.send_socket_mode_response(response)


def start_socket_mode_client():
    """Start the Socket Mode client."""
    try:
        # Create Socket Mode client
        client = SocketModeClient(app_token=SLACK_APP_TOKEN, web_client=WebClient(token=SLACK_TOKEN))

        # Set up request handler
        client.socket_mode_request_listeners.append(handle_socket_mode_request)

        # Start the client
        logger.info("Starting Slack Socket Mode client...")
        client.connect()

    except Exception as e:
        logger.error(f"Failed to start Socket Mode client: {e}")
        raise
