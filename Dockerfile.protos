FROM alexbot

USER 0

ADD requirements-protos.txt .

RUN pip3 install --break-system-packages -r requirements-protos.txt

CMD mkdir -p ./generated && \
    python-grpc-tools-protoc -Iprotos/src $(find -name '*.proto') --python_out=./generated --grpc_python_out=./generated && \
    find ./generated -type d -exec touch {}/__init__.py \; && \
    sed -i -e '/^from google.protobuf/b; /^from grpc/b; s/^from /from generated./' $(find ./generated -name '*.py')
