{"data_mtime": 1753456362, "dep_lines": [3, 8, 1, 2, 4, 5, 13, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.machinery", "importlib.readers", "sys", "_typeshed", "types", "typing_extensions", "_frozen_importlib_external", "builtins", "_frozen_importlib", "abc", "importlib", "importlib.resources", "importlib.resources.abc", "typing"], "hash": "823c44e8278a8d36fd11aea3ea656af63f1bfc00", "id": "zipimport", "ignore_all": true, "interface_hash": "81560530b21014787806607748432d86f9ca7218", "mtime": 1753456353, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/usr/local/lib/python3.12/dist-packages/mypy/typeshed/stdlib/zipimport.pyi", "plugin_data": null, "size": 2079, "suppressed": [], "version_id": "1.17.0"}