import math
import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional

import psycopg
from prometheus_api_client import PrometheusConnect

from config import PROMETHEUS_URL

logger = logging.getLogger(__name__)


@dataclass
class EcefCoordinate:
    """Represents ECEF (Earth-Centered, Earth-Fixed) coordinates."""

    x: float
    y: float
    z: float

    def distance_to(self, other: "EcefCoordinate") -> float:
        """Calculate Euclidean distance to another ECEF coordinate in meters."""
        dx = self.x - other.x
        dy = self.y - other.y
        dz = self.z - other.z
        return math.hypot(dx, dy, dz)

    def __str__(self) -> str:
        return f"ECEF({self.x:.2f}, {self.y:.2f}, {self.z:.2f})"


def get_db_connection(db_url: str) -> psycopg.Connection:
    """Create a database connection using a connection string."""
    logger = logging.getLogger(__name__)
    try:
        conn = psycopg.connect(db_url)
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise


def get_prometheus_connection() -> PrometheusConnect:
    """Create and return a Prometheus connection."""
    logger = logging.getLogger(__name__)
    try:
        prom = PrometheusConnect(url=PROMETHEUS_URL, disable_ssl=True)
        logger.info(f"Connected to Prometheus at {PROMETHEUS_URL}")
        return prom
    except Exception as e:
        logger.error(f"Failed to connect to Prometheus at {PROMETHEUS_URL}: {e}")
        raise


def get_metric_data(
    prom: PrometheusConnect, metric_name: str, robot_id: str, start_time: datetime, end_time: datetime
) -> List[Dict]:
    """Get metric data from Prometheus for a specific robot and time range."""
    logger = logging.getLogger(__name__)
    try:
        data = prom.get_metric_range_data(
            metric_name=metric_name, label_config={"robot": robot_id}, start_time=start_time, end_time=end_time
        )
        logger.debug(f"Retrieved {len(data)} data points for {metric_name} for robot {robot_id}")
        return data
    except Exception as e:
        logger.error(f"Failed to get {metric_name} data for robot {robot_id}: {e}")
        return []


def mm_to_inch(mm):
    return mm / 25.4


def get_robot_by_channel(alexbot_conn: psycopg.Connection, channel: str) -> Optional[str]:
    """Get robot ID from channel name using robots_to_monitor table."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                "SELECT robot_id FROM robots_to_monitor WHERE support_channel = %s",
                (channel,),
            )
            result = cursor.fetchone()
            return result[0] if result else None
    except Exception as e:
        logger.error(f"Failed to get robot for channel {channel}: {e}")
        return None
