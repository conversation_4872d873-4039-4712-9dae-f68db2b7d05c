import logging
from datetime import datetime, timedelta
from typing import Dict, Optional

import psycopg
from slack_sdk import Web<PERSON>lient

from config import (
    SPEED_DIFFERENCE_THRESHOLD_MPH,
    SPEED_MONITORING_WINDOW_MINUTES,
    SPEED_ALERT_INTERVAL_MINUTES,
    SPEED_CONSTANCY_TOLERANCE_MPH,
    SPEED_CONSTANCY_WINDOW_MINUTES,
)
from visualization_requests import request_visualization
from utils import get_prometheus_connection, get_metric_data

logger = logging.getLogger(__name__)


def check_speed_compliance(robot_id: str) -> Optional[Dict]:
    """
    Check if robot operator is following target speed for the configured time window.
    Returns None if no data or not weeding continuously, otherwise returns alert data.
    """
    try:
        prom = get_prometheus_connection()

        # Calculate time range (configured window)
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=SPEED_MONITORING_WINDOW_MINUTES)

        # Get weeding status
        weeding_data = get_metric_data(prom, "commander_is_weeding", robot_id, start_time, end_time)

        if not weeding_data:
            logger.debug(f"No weeding data found for robot {robot_id}")
            return None

        # Check if robot was weeding continuously for the entire time window
        all_weeding = True

        for data_point in weeding_data:
            for timestamp, value in data_point.get("values", []):
                if value != "1":
                    all_weeding = False

        if not all_weeding:
            logger.debug(
                f"Robot {robot_id} was not weeding continuously for the past {SPEED_MONITORING_WINDOW_MINUTES} minutes"
            )
            return None

        # Get current velocity data
        current_velocity_data = get_metric_data(prom, "commander_velocity_current_mph", robot_id, start_time, end_time)
        target_velocity_data = get_metric_data(prom, "commander_velocity_target_mph", robot_id, start_time, end_time)

        if not current_velocity_data or not target_velocity_data:
            logger.warning(f"Missing velocity data for robot {robot_id}")
            return None

        # Check if speed difference exceeds threshold for ALL observations
        all_speed_violations = True

        # Create a mapping of timestamps to values for easier comparison
        current_velocity_values = []
        target_velocity_values = []

        for data_point in current_velocity_data:
            current_velocity_values.extend(data_point.get("values", []))

        for data_point in target_velocity_data:
            target_velocity_values.extend(data_point.get("values", []))

        current_vel_map = {timestamp: float(value) for timestamp, value in current_velocity_values}
        target_vel_map = {timestamp: float(value) for timestamp, value in target_velocity_values}

        # Get all unique timestamps that have both current and target velocity data
        all_timestamps = set(current_vel_map.keys()) & set(target_vel_map.keys())

        if not all_timestamps:
            logger.warning(f"No matching timestamps found for velocity data for robot {robot_id}")
            return None

        for timestamp in all_timestamps:
            current_vel = current_vel_map[timestamp]
            target_vel = target_vel_map[timestamp]
            speed_diff = abs(current_vel - target_vel)

            if speed_diff <= SPEED_DIFFERENCE_THRESHOLD_MPH:
                all_speed_violations = False
                logger.info(
                    f"Robot {robot_id} speed difference {speed_diff:.2f} mph at {timestamp} "
                    f"is within threshold ({SPEED_DIFFERENCE_THRESHOLD_MPH} mph)"
                )
                break

        if not all_speed_violations:
            logger.info(
                f"Robot {robot_id} speed difference was not consistently "
                f"above {SPEED_DIFFERENCE_THRESHOLD_MPH} mph threshold"
            )
            return None

        # Get the most recent values for the alert data
        current_velocity_values.sort(key=lambda x: x[0], reverse=True)
        target_velocity_values.sort(key=lambda x: x[0], reverse=True)

        current_velocity = float(current_velocity_values[0][1])
        target_velocity = float(target_velocity_values[0][1])
        speed_diff = abs(current_velocity - target_velocity)

        # Use the timestamp from the most recent observation
        last_observation_timestamp_ms = int(current_velocity_values[0][0] * 1000)  # Convert to milliseconds

        logger.warning(
            f"Speed compliance alert for robot {robot_id}: "
            f"current={current_velocity:.2f} mph, target={target_velocity:.2f} mph, "
            f"difference={speed_diff:.2f} mph (threshold: {SPEED_DIFFERENCE_THRESHOLD_MPH} mph)"
        )

        return {
            "robot_id": robot_id,
            "is_weeding": True,
            "current_velocity": current_velocity,
            "target_velocity": target_velocity,
            "speed_difference": speed_diff,
            "timestamp_ms": last_observation_timestamp_ms,
            "alert_type": "difference",
        }

    except Exception as e:
        logger.error(f"Error checking speed compliance for robot {robot_id}: {e}")
        return None


def get_last_speed_alert(alexbot_conn: psycopg.Connection, robot_id: str) -> Optional[Dict]:
    """Get the last speed alert for a robot from alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                "SELECT timestamp_ms, is_weeding, current_velocity, target_velocity, speed_difference "
                "FROM last_speed_alert WHERE robot_id = %s",
                (robot_id,),
            )
            result = cursor.fetchone()
            if result:
                return {
                    "timestamp_ms": result[0],
                    "is_weeding": result[1],
                    "current_velocity": result[2],
                    "target_velocity": result[3],
                    "speed_difference": result[4],
                }
            return None
    except Exception as e:
        logger.error(f"Failed to get last speed alert for robot {robot_id}: {e}")
        return None


def record_speed_alert(alexbot_conn: psycopg.Connection, robot_id: str, alert_data: Dict):
    """Record or update the last speed alert for a robot in alexbotdb."""
    try:
        # Use the timestamp from the observation (already in milliseconds)
        timestamp_ms = int(alert_data["timestamp_ms"])

        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                INSERT INTO last_speed_alert (
                    robot_id, timestamp_ms, is_weeding,
                    current_velocity, target_velocity, speed_difference
                )
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (robot_id) DO UPDATE SET
                    timestamp_ms = EXCLUDED.timestamp_ms,
                    is_weeding = EXCLUDED.is_weeding,
                    current_velocity = EXCLUDED.current_velocity,
                    target_velocity = EXCLUDED.target_velocity,
                    speed_difference = EXCLUDED.speed_difference
                """,
                (
                    robot_id,
                    timestamp_ms,
                    alert_data["is_weeding"],
                    alert_data["current_velocity"],
                    alert_data["target_velocity"],
                    alert_data["speed_difference"],
                ),
            )
            alexbot_conn.commit()
            logger.info(f"Recorded/updated speed alert for robot {robot_id}")
    except Exception as e:
        logger.error(f"Failed to record speed alert for robot {robot_id}: {e}")
        alexbot_conn.rollback()


def should_send_alert(alexbot_conn: psycopg.Connection, robot_id: str, alert_data: Dict) -> bool:
    """
    Check if we should send an alert based on the last alert time.
    Only send if no alert was sent in the last configured interval.
    """
    try:
        last_alert = get_last_speed_alert(alexbot_conn, robot_id)
        if not last_alert:
            return True

        # Check if more than the configured interval has passed since last alert
        current_timestamp_ms = int(alert_data["timestamp_ms"])
        time_since_last_alert_ms = current_timestamp_ms - last_alert["timestamp_ms"]
        interval_ms = SPEED_ALERT_INTERVAL_MINUTES * 60 * 1000  # Convert minutes to milliseconds
        return time_since_last_alert_ms > interval_ms

    except Exception as e:
        logger.error(f"Error checking if should send alert for robot {robot_id}: {e}")
        return True  # Default to sending alert if there's an error


def send_speed_alert(alert_data: Dict, client: WebClient, channel: str) -> None:
    """Send speed alert to Slack."""
    try:
        robot_id = alert_data["robot_id"]
        current_velocity = alert_data["current_velocity"]
        target_velocity = alert_data["target_velocity"]
        speed_difference = alert_data["speed_difference"]
        alert_type = alert_data["alert_type"]

        # Create alert message based on type
        if alert_type == "difference":
            message = (
                f"🚨 *Speed Compliance Alert: {robot_id}* 🚨\n"
                f"Current: *{current_velocity:.2f}* mph | "
                f"Target: *{target_velocity:.2f}* mph | "
                f"Difference: *{speed_difference:.2f}* mph\n\n"
                f"<https://customer.cloud.carbonrobotics.com/fleet/robots/{robot_id}|View in Portal> | "
                f"<https://grafana.cloud.carbonrobotics.com/d/510H15u7k/slayer-dashboard?orgId=1&from=now-3h&to=now&var-robot_name={robot_id}&refresh=1m&viewPanel=52|View in Grafana>"  # noqa: E501
            )
        elif alert_type == "constancy":
            message = (
                f"🚨 *Speed Constancy Alert: {robot_id}* 🚨\n"
                f"Speed: *{current_velocity:.2f}* mph | "
                f"Tolerance: ±{SPEED_CONSTANCY_TOLERANCE_MPH} mph | "
                f"Duration: {SPEED_CONSTANCY_WINDOW_MINUTES} minutes\n\n"
                f"<https://customer.cloud.carbonrobotics.com/fleet/robots/{robot_id}|View in Portal> | "
                f"<https://grafana.cloud.carbonrobotics.com/d/510H15u7k/slayer-dashboard?orgId=1&from=now-3h&to=now&var-robot_name={robot_id}&refresh=1m&viewPanel=52|View in Grafana>"  # noqa: E501
            )
        else:
            logger.error(f"Unknown alert type: {alert_type}")
            return

        # Send to Slack
        response = client.chat_postMessage(channel=channel, text=message)
        logger.info(f"Sent Slack notification for robot {robot_id} to channel {channel}: {response}")

    except Exception as e:
        logger.error(f"Failed to send Slack notification to channel {channel}: {e}")


def monitor_speed_compliance(
    robot_id: str,
    target_channel: str,
    alexbot_conn: psycopg.Connection,
    slack_client: WebClient,
) -> None:
    """Monitor speed compliance and send alerts if needed."""
    try:
        compliance_alert_data = check_speed_compliance(robot_id)
        if compliance_alert_data and should_send_alert(alexbot_conn, robot_id, compliance_alert_data):
            # Send Slack notification
            send_speed_alert(compliance_alert_data, slack_client, target_channel)

            # Request visualization
            request_visualization(alexbot_conn, robot_id, target_channel)

            # Record the alert (this will update existing record or create new one)
            record_speed_alert(alexbot_conn, robot_id, compliance_alert_data)

    except Exception as e:
        logger.error(f"Error processing speed compliance for robot {robot_id}: {e}")


def check_speed_constancy(robot_id: str) -> Optional[Dict]:
    """
    Check if robot speed has remained constant within tolerance for the alert interval period.
    Returns None if no data or not weeding, otherwise returns alert data.
    """
    try:
        prom = get_prometheus_connection()

        # Calculate time range (constancy window period)
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=SPEED_CONSTANCY_WINDOW_MINUTES)

        # Get weeding status
        weeding_data = get_metric_data(prom, "commander_is_weeding", robot_id, start_time, end_time)

        if not weeding_data:
            logger.info(f"No weeding data found for robot {robot_id}")
            return None

        # Check if robot was weeding for most of the time period
        weeding_count = 0
        total_observations = 0

        for data_point in weeding_data:
            for timestamp, value in data_point.get("values", []):
                total_observations += 1
                if value == "1":
                    weeding_count += 1

        # Require all observations to show weeding
        if total_observations == 0 or weeding_count != total_observations:
            logger.info(
                f"Robot {robot_id} was not weeding for all observations "
                f"in the past {SPEED_CONSTANCY_WINDOW_MINUTES} minutes"
            )
            return None

        # Get current velocity data
        current_velocity_data = get_metric_data(prom, "commander_velocity_current_mph", robot_id, start_time, end_time)

        if not current_velocity_data:
            logger.warning(f"Missing velocity data for robot {robot_id}")
            return None

        # Extract all velocity values
        velocity_values = []
        for data_point in current_velocity_data:
            velocity_values.extend(data_point.get("values", []))

        if len(velocity_values) < 2:
            logger.info(f"Insufficient velocity data for robot {robot_id}")
            return None

        # Check if all velocities are within tolerance of each other
        velocities = [float(value) for timestamp, value in velocity_values]
        min_velocity = min(velocities)
        max_velocity = max(velocities)

        # Get the most recent values for the alert data
        velocity_values.sort(key=lambda x: x[0], reverse=True)
        current_velocity = float(velocity_values[0][1])
        last_observation_timestamp_ms = int(velocity_values[0][0] * 1000)  # Convert to milliseconds

        if (
            min_velocity < current_velocity - SPEED_CONSTANCY_TOLERANCE_MPH
            or max_velocity > current_velocity + SPEED_CONSTANCY_TOLERANCE_MPH
        ):
            logger.info(
                f"Robot {robot_id} speed is outside of "
                f"{current_velocity:.2f} mph ± {SPEED_CONSTANCY_TOLERANCE_MPH} mph "
                f"(min: {min_velocity:.2f} mph, max: {max_velocity:.2f} mph)"
            )
            return None

        logger.warning(
            f"Speed constancy alert for robot {robot_id}: "
            f"speed remained at {current_velocity:.2f} mph ± {SPEED_CONSTANCY_TOLERANCE_MPH} mph "
            f"for {SPEED_CONSTANCY_WINDOW_MINUTES} minutes (min: {min_velocity:.2f} mph, max: {max_velocity:.2f} mph)"
        )

        return {
            "robot_id": robot_id,
            "is_weeding": True,
            "current_velocity": current_velocity,
            "target_velocity": current_velocity,  # Same as current for constancy alerts
            "speed_difference": 0.0,  # No difference for constancy
            "timestamp_ms": last_observation_timestamp_ms,
            "alert_type": "constancy",
        }

    except Exception as e:
        logger.error(f"Error checking speed constancy for robot {robot_id}: {e}")
        return None


def monitor_speed_constancy(
    robot_id: str,
    target_channel: str,
    alexbot_conn: psycopg.Connection,
    slack_client: WebClient,
) -> None:
    """Monitor speed constancy and send alerts if needed."""
    try:
        constancy_alert_data = check_speed_constancy(robot_id)
        if constancy_alert_data and should_send_alert(alexbot_conn, robot_id, constancy_alert_data):
            # Send Slack notification
            send_speed_alert(constancy_alert_data, slack_client, target_channel)

            # Request visualization
            request_visualization(alexbot_conn, robot_id, target_channel)

            # Record the alert (this will update existing record or create new one)
            record_speed_alert(alexbot_conn, robot_id, constancy_alert_data)

    except Exception as e:
        logger.error(f"Error processing speed constancy for robot {robot_id}: {e}")
