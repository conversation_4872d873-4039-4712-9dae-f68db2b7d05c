# AlexBot Robot Monitoring Service

This application monitors robot locations, speed compliance, and plant profile changes by querying PostgreSQL databases, Prometheus metrics, and ROSY API, sending Slack notifications when robots move more than a specified distance threshold, when operators don't follow target speeds, or when plant profiles change.

## Features

- Monitors robots listed in the `robots_to_monitor` table
- Queries spatial metrics from the portal database
- Tracks robot locations in the alexbotdb database
- Monitors robot operator speed compliance using Prometheus metrics
- Monitors plant profile changes using portal database and ROSY API
- Monitors thinning box configurations and alerts when thinning box is too big
- Sends Slack notifications when robots move more than 1000 meters
- Sends Slack notifications when speed difference exceeds 0.5 mph
- Sends Slack notifications when plant profiles change
- Sends Slack notifications when thinning box size exceeds threshold
- Configurable monitoring interval and distance threshold
- Comprehensive logging
- Timestamp filtering to prevent processing old data and handle bad timestamps

## Database Schema

### AlexBot Database (alexbotdb)

#### robots_to_monitor table
```sql
CREATE TABLE robots_to_monitor (
    robot_id TEXT PRIMARY KEY,
    support_channel TEXT NOT NULL,
    enable_new_field_alert BOOLEAN NOT NULL DEFAULT FALSE,
    enable_speed_compliance_alert BOOLEAN NOT NULL DEFAULT FALSE,
    enable_speed_constancy_alert BOOLEAN NOT NULL DEFAULT FALSE,
    enable_plant_profile_change_alert BOOLEAN NOT NULL DEFAULT FALSE,
    enable_crop_model_change_alert BOOLEAN NOT NULL DEFAULT FALSE,
    enable_thinning_box_alert BOOLEAN NOT NULL DEFAULT FALSE
);
```

#### last_location table
```sql
CREATE TABLE last_location (
    robot_id TEXT PRIMARY KEY,
    timestamp_ms BIGINT NOT NULL,
    ecef_x NUMERIC NOT NULL,
    ecef_y NUMERIC NOT NULL,
    ecef_z NUMERIC NOT NULL
);
```

#### last_speed_alert table
```sql
CREATE TABLE last_speed_alert (
    robot_id TEXT PRIMARY KEY,
    timestamp_ms BIGINT NOT NULL,
    is_weeding BOOLEAN NOT NULL,
    current_velocity NUMERIC NOT NULL,
    target_velocity NUMERIC NOT NULL,
    speed_difference NUMERIC NOT NULL
);
```

#### last_plant_profile table
```sql
CREATE TABLE last_plant_profile (
    robot_id TEXT PRIMARY KEY,
    timestamp_ms BIGINT NOT NULL,
    profile_id TEXT NOT NULL,
    profile_name TEXT NOT NULL
);
```

#### last_crop_model table
```sql
CREATE TABLE last_crop_model (
    robot_id TEXT PRIMARY KEY,
    timestamp_ms BIGINT NOT NULL,
    model TEXT NOT NULL
);
```

#### last_thinning_box_alert table
```sql
CREATE TABLE last_thinning_box_alert (
    robot_id TEXT PRIMARY KEY,
    timestamp_ms BIGINT NOT NULL,
    thinning_profile TEXT NOT NULL,
    max_spacing NUMERIC NOT NULL,
    ideal_spacing NUMERIC NOT NULL,
    min_spacing NUMERIC NOT NULL
);
```

#### visualization_requests table
```sql
CREATE TABLE visualization_requests (
    robot_id TEXT PRIMARY KEY,
    requested_timestamp_ms BIGINT NOT NULL,
    completed_timestamp_ms BIGINT,
    channel TEXT NOT NULL
);
```

### Portal Database (portal)

The application queries the `robots` and `spatial_metrics` tables to get robot information and location data.

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Configure environment variables or update the `config.py` file with your database credentials:

```bash
# Database Configuration
export ALEXBOT_DB_URL="******************************************/alexbotdb"
export PORTAL_DB_URL="******************************************/portal"

# Slack Configuration
export SLACK_TOKEN=xoxb-your-slack-token-here
export SLACK_APP_TOKEN=xapp-1-your-slack-token-here
export SUPPORT_CHANNEL_OVERRIDE="#testing"

# Prometheus Configuration
export PROMETHEUS_URL="http://localhost:9090"

# ROSY Configuration
export ROSY_URL="http://localhost:8080"

# Monitoring Configuration
export DISTANCE_THRESHOLD_METERS=1000
export MONITORING_INTERVAL_SECONDS=60

# Speed Monitoring Configuration
export SPEED_DIFFERENCE_THRESHOLD_MPH=0.5
export SPEED_MONITORING_WINDOW_MINUTES=5

# Thinning Box Monitoring Configuration
export MAX_THINNING_BOX_THRESHOLD_INCHES=7.0
export THINNING_BOX_ALERT_INTERVAL_MINUTES=30
export VISUALIZATION_RATE_LIMIT_MINUTES=15
export VISUALIZATION_WEEDING_REQUIREMENT_MINUTES=3

# Logging Configuration
export LOG_LEVEL=INFO
```

**Connection String Format:**
```
postgres://username:password@hostname:port/database
```

**Example:**
```
postgres://portal:<EMAIL>/portal
```

**Note:** Each robot in the `robots_to_monitor` table must have a `support_channel` specified. Notifications will be sent to the channel assigned to each individual robot.

### Alert Enable Flags

The `robots_to_monitor` table includes boolean flags to control which alerts are enabled for each robot:

- `enable_new_field_alert`: Enables movement/distance alerts (default: FALSE)
- `enable_speed_compliance_alert`: Enables speed difference alerts (default: FALSE)
- `enable_speed_constancy_alert`: Enables speed constancy alerts (default: FALSE)
- `enable_plant_profile_change_alert`: Enables plant profile change alerts (default: FALSE)
- `enable_crop_model_change_alert`: Enables crop model change alerts (default: FALSE)
- `enable_thinning_box_alert`: Enables alerts when thinning box size is too big (default: FALSE)

Only enabled alert types will be checked for each robot, allowing fine-grained control over monitoring behavior.

## Project Structure

- `app.py` - Main monitoring application
- `config.py` - Configuration management
- `utils.py` - Utility classes and database connection functions
- `robot_movement.py` - Robot movement monitoring functions
- `speed_monitoring.py` - Robot speed compliance monitoring functions
- `plant_profile_monitoring.py` - Plant profile change monitoring functions
- `crop_model_monitoring.py` - Crop model change monitoring functions
- `thinning_box_monitoring.py` - Thinning box size monitoring functions
- `remoteit.py` - Remote.IT API integration for remote device connections
- `requirements.txt` - Python dependencies
- `README.md` - This documentation

## Usage

Run the monitoring service:

```bash
python app.py
```

The service will:
1. Connect to PostgreSQL databases and Prometheus
2. Get the list of robots to monitor from `robots_to_monitor` table
3. For each robot:
   - **Movement Monitoring:**
     - Query the latest spatial metrics from the portal database
     - Extract ECEF coordinates from the JSON block data
     - Compare with the last known location
     - Send Slack notification if distance exceeds threshold
     - Update the last known location
   - **Speed Compliance Monitoring:**
     - Query Prometheus for weeding status and velocity metrics
     - Check if robot is weeding and speed difference exceeds 0.5 mph
     - Send Slack notification with historical data if violation detected
     - Record alert in speed_alerts table
   - **Plant Profile Monitoring:**
     - Query health_logs from portal database for field_config
     - Extract active_category_collection_id from field_config
     - Get profile name from ROSY API
     - Send Slack notification if profile has changed
     - Record current profile in last_plant_profile table
   - **Crop Model Monitoring:**
     - Query health_logs from portal database for model
     - Extract model type (first part before '-')
     - Send Slack notification if model type has changed
     - Record current model in last_crop_model table
   - **Thinning Box Monitoring:**
     - Query health_logs from portal database for field_config
     - Check if robot is thinning and get active_thinning_config
     - Query profiles table for thinning profile configuration
     - Calculate thinning box size: maxYSearchRadius - minKeepout.height / 2
     - Send Slack notification if thinning box size exceeds threshold
     - Record alert in last_thinning_box_alert table
4. Wait for the configured interval (default: 60 seconds)
5. Repeat the monitoring cycle

## Speed Compliance Monitoring

The application now monitors robot operator speed compliance using Prometheus metrics:

### Metrics Monitored
- `commander_is_weeding` - Whether the robot is currently weeding
- `commander_velocity_current_mph` - Current velocity in mph
- `commander_velocity_target_mph` - Target velocity in mph

### Alert Conditions
An alert is triggered when ALL of the following are true for the configured time window (default: 5 minutes):
1. `commander_is_weeding = '1'` for the ENTIRE time window (robot was weeding continuously)
2. `abs(current_velocity - target_velocity) > SPEED_DIFFERENCE_THRESHOLD_MPH` for ALL observations in the time window (speed difference consistently exceeds threshold, default: 0.5 mph)

### Alert Features
- **Anti-Spam**: Only sends alerts every 30 minutes per robot
- **Database Tracking**: Records the last alert for each robot in `last_speed_alert` table
- **Portal Link**: Includes direct link to robot in Portal

### Example Slack Alert
```
🚨 Speed Compliance Alert: reaper121 🚨
Current: 2.50 mph | Target: 1.80 mph | Difference: 0.70 mph

View in Portal | View in Grafana
```

## Plant Profile Monitoring

The application monitors plant profile changes by querying the portal database and ROSY API:

### Data Sources
- **Portal Database**: Queries `health_logs` table for `field_config` containing `active_category_collection_id`
- **ROSY API**: Resolves profile names using `/internal/v1/profiles/{profile_id}` endpoint

### Alert Conditions
An alert is triggered when:
1. A robot's `active_category_collection_id` changes from the last known value
2. The change is detected in the `health_logs` table with valid timestamps

### Alert Features
- **Database Tracking**: Records the last profile for each robot in `last_plant_profile` table
- **Profile Name Resolution**: Fetches human-readable profile names from ROSY API
- **Portal Link**: Includes direct link to robot in Portal
- **Timestamp Filtering**: Only processes new data since last check

### Example Slack Alert
```
🌱 Plant Profile Change Alert: reaper121 🌱
Old Profile: Teigan's Lettuce Model - Thinning (a3e5781f-b05b-4ac2-a229-b6d2f710abc5)
New Profile: New Crop Model (b96ec812-68a6-4e07-9c15-ded811d1a7cf)

View in Portal
```

## Crop Model Monitoring

The application monitors crop model changes by querying the portal database:

### Data Sources
- **Portal Database**: Queries `health_logs` table for `model` field containing the current crop model

### Alert Conditions
An alert is triggered when:
1. A robot's model type changes from the last known value
2. The model type is extracted as the first group of characters before the first `-` in the model string
3. The change is detected in the `health_logs` table with valid timestamps

### Alert Features
- **Database Tracking**: Records the last model for each robot in `last_crop_model` table
- **Model Type Extraction**: Extracts model type (e.g., 'prt' from 'prt-20250522-qmyl2406pr')
- **Portal Link**: Includes direct link to robot in Portal
- **Timestamp Filtering**: Only processes new data since last check

### Example Slack Alert
```
🤖 Crop Model Type Change Alert: reaper121 🤖
Old Model: prt-20250522-qmyl2406pr (Type: prt)
New Model: fut-20250523-abc123def (Type: fut)

View in Portal
```

## Thinning Box Monitoring

The application monitors thinning box configurations and alerts when the thinning box is too big:

### Data Sources
- **Portal Database**: Queries `health_logs` table for `field_config` containing `is_thinning` and `active_thinning_config`
- **Portal Database**: Queries `profiles` table for thinning profile configuration

### Alert Conditions
An alert is triggered when:
1. A robot is currently thinning (`is_thinning = true`)
2. The thinning box size calculation exceeds the configured threshold (default: 7.0)
3. The calculation is: `maxYSearchRadius - minKeepout.height / 2`

### Alert Features
- **Database Tracking**: Records the last alert for each robot in `last_thinning_box_alert` table
- **Anti-Spam**: Only sends alerts every 30 minutes per robot (configurable)
- **Portal Link**: Includes direct link to robot in Portal
- **Timestamp Filtering**: Only processes new data since last check

### Example Slack Alert
```
📦 Thinning Box Too Big Alert: reaper121 📦
Profile: Headlettuce40
Min Thinning Spacing: 5.0" | Ideal Thinning Spacing: 10.0" | Max Thinning Spacing: 13.0"
Thinning Box Size: 8.0" (Threshold: 7.0")

View in Portal
```

## Visualization

The application can generate and send visualizations of robot weeding activity to Slack channels. Visualizations are automatically requested when speed compliance or constancy alerts are triggered.

### Visualization Requirements

Before a visualization can be generated, the robot must meet the following requirements:

1. **Weeding Requirement**: The robot must be weeding continuously for the configured duration (default: 3 minutes) prior to visualization
2. **Weeding Enabled**: The robot's weeding system must be enabled (`weeding_enabled = true`)
3. **Rate Limiting**: Visualizations are rate-limited to prevent spam (default: 15 minutes between requests)

### Visualization Features

- **Real-time Data**: Uses live data from the robot's weeding system
- **Multi-row Support**: Shows visualization for all active rows
- **Comprehensive Legend**: Includes crop, weed, kept crop, bands, kill boxes, and thinning markers
- **Automatic Cleanup**: Remote.IT sessions are automatically disconnected after visualization
- **Error Handling**: Graceful handling of connection failures and data issues

### Visualization Configuration

- `VISUALIZATION_RATE_LIMIT_MINUTES`: Rate limit in minutes between visualization requests (default: 15)
- `VISUALIZATION_WEEDING_REQUIREMENT_MINUTES`: Required duration in minutes that robot must be weeding before visualization (default: 3)

### Example Visualization Message
```
:carbon-rooster-lasers: Visualization for reaper121 :carbon-rooster-lasers:
Legend: :large_green_circle: Crop, :red_circle: Weed, :large_purple_circle: Kept crop, 
:large_yellow_square: Band, :large_purple_square: Kill Box, 
:negative_squared_cross_mark: Marked for thinning
```

## Speed Monitoring

The application monitors robot speed compliance and sends alerts when:

1. **Speed Difference Alerts**: A robot is weeding continuously for the past 5 minutes and the absolute difference between current and target speed exceeds the configured threshold (default: 0.5 mph).

2. **Speed Constancy Alerts**: A robot's speed remains constant (within a small tolerance) for the configured window period (default: 15 minutes), which may indicate the robot is stuck or not responding to speed commands.

### Speed Monitoring Configuration

- `SPEED_DIFFERENCE_THRESHOLD_MPH`: Threshold for speed difference alerts (default: 0.5)
- `SPEED_MONITORING_WINDOW_MINUTES`: Time window in minutes for checking continuous weeding (default: 5)
- `SPEED_ALERT_INTERVAL_MINUTES`: Anti-spam interval in minutes between alerts (default: 30)
- `SPEED_CONSTANCY_TOLERANCE_MPH`: Tolerance in mph for speed constancy detection (default: 0.01)
- `SPEED_CONSTANCY_WINDOW_MINUTES`: Time window in minutes for checking speed constancy (default: 15)

## Configuration

### Environment Variables

- `ALEXBOT_DB_URL`: AlexBot database connection string
- `PORTAL_DB_URL`: Portal database connection string
- `SLACK_TOKEN`: Slack bot token
- `SLACK_APP_TOKEN`: Slack app token
- `SUPPORT_CHANNEL_OVERRIDE`: Slack channel override for notifications (optional)
  - If set, all alerts will be sent to this channel instead of per-robot channels
  - If not set, each robot will use its assigned support channel from the database
  - Example: `export SUPPORT_CHANNEL_OVERRIDE="#testing"`
- `PROMETHEUS_URL`: Prometheus server URL (default: http://localhost:9090)
- `ROSY_URL`: ROSY API server URL (default: http://localhost:8080)
- `DISTANCE_THRESHOLD_METERS`: Distance threshold for alerts (default: 1000)
- `MONITORING_INTERVAL_SECONDS`: Monitoring interval in seconds (default: 60)
- `SPEED_DIFFERENCE_THRESHOLD_MPH`: Speed difference threshold for alerts (default: 0.5)
- `SPEED_MONITORING_WINDOW_MINUTES`: Speed monitoring window in minutes (default: 5)
- `SPEED_ALERT_INTERVAL_MINUTES`: Anti-spam interval between alerts (default: 30)
- `SPEED_CONSTANCY_TOLERANCE_MPH`: Tolerance for speed constancy detection (default: 0.01)
- `SPEED_CONSTANCY_WINDOW_MINUTES`: Time window in minutes for checking speed constancy (default: 15)
- `MAX_THINNING_BOX_THRESHOLD_INCHES`: Threshold for thinning box size alerts (default: 7.0)
- `THINNING_BOX_ALERT_INTERVAL_MINUTES`: Anti-spam interval in minutes between thinning alerts (default: 30)
- `VISUALIZATION_RATE_LIMIT_MINUTES`: Rate limit in minutes between visualization requests (default: 15)
- `VISUALIZATION_WEEDING_REQUIREMENT_MINUTES`: Required duration in minutes that robot must be weeding before visualization (default: 3)
- `LOG_LEVEL`: Logging level (default: INFO)

### Distance Calculation

The application calculates Euclidean distance between ECEF (Earth-Centered, Earth-Fixed) coordinates. This provides accurate distance measurements regardless of the robot's location on Earth.

### Timestamp Filtering

The application includes intelligent timestamp filtering to ensure data quality:

- **Incremental Processing**: Only processes spatial metrics newer than the last processed timestamp
- **Future Timestamp Protection**: Ignores timestamps more than 15 minutes into the future to handle bad data
- **Data Consistency**: Prevents duplicate processing and ensures chronological order

This filtering mechanism helps maintain data integrity and prevents issues with incorrectly timestamped records in the database.

## Slack Notifications

When a robot moves more than the configured distance threshold, a Slack message is sent to the robot's assigned support channel with:

- Robot ID
- Distance moved
- Previous and new ECEF coordinates
- Timestamp

Each robot can have its own dedicated support channel, allowing for targeted notifications and better organization of alerts.

## Logging

The application provides comprehensive logging at the configured level. Logs include:
- Database connection status
- Robot monitoring progress
- Distance calculations
- Slack notification status
- Error handling

## Remote.IT Integration

The application includes integration with Remote.IT's GraphQL API for managing remote connections to provisioned devices.

### Features

- **Device Management**: List and query all devices in your Remote.IT account
- **Connection Management**: Create and manage remote connections to device services
- **Service Discovery**: Automatically discover available services on devices
- **Status Monitoring**: Check device online status
- **Pagination Support**: Handle large device lists with pagination
- **Environment Variable Authentication**: Uses R3_ACCESS_KEY_ID and R3_SECRET_ACCESS_KEY

### Usage

```python
from remoteit import RemoteItAPI

# Create API client (uses environment variables)
client = RemoteItAPI()

# List all devices with pagination
devices = client.get_devices(size=100, from_index=0, sort="name")
for device in devices:
    status = "🟢 Online" if device.online else "🔴 Offline"
    print(f"Device: {device.name} - {status}")
    
    # Show services for each device
    for service in device.services:
        enabled = "✅" if service.enabled else "❌"
        print(f"  - {service.name} ({service.type}) on port {service.port} {enabled}")

# Find a specific device by name
device = client.find_device_by_name("my-device")
if device:
    print(f"Found device: {device.name} (Online: {device.online})")

# Connect to a device service
connection_result = client.connect_to_device("my-device", "ssh")
if connection_result:
    session_id, host, port = connection_result
    print(f"Connected: {host}:{port}")
    
    # Disconnect when done
    client.disconnect_session(session_id)
```

### Credentials Setup

Set the following environment variables in your shell or environment:

```bash
export R3_ACCESS_KEY_ID=your-access-key-id
export R3_SECRET_ACCESS_KEY=your-secret-access-key
```

### API Methods

#### Device Management
- `get_devices(size=1000, from_index=0, sort="name")` - Get all devices with pagination
- `get_device_by_id(device_id)` - Get a specific device by ID
- `find_device_by_name(device_name)` - Find a device by name

#### Connection Management
- `connect_to_device(device_name, service_name)` - Connect to a device service
- `connect_to_service(service_id)` - Connect directly to a service
- `disconnect_session(session_id)` - Disconnect a session

### Data Structures

#### RemoteItDevice
```python
@dataclass
class RemoteItDevice:
    device_id: str
    name: str
    services: List[RemoteItService]
    online: bool = False
```

#### RemoteItService
```python
@dataclass
class RemoteItService:
    service_id: str
    name: str
    type: str
    port: int
    host: str
    enabled: bool = True
```

### Error Handling

The module includes comprehensive error handling for:
- Missing environment variables
- API authentication failures
- Device not found errors
- Offline device connections
- Service connection failures
- GraphQL query errors

All methods return appropriate error values (None, False, empty lists) and log detailed error messages for debugging.

## Error Handling

The application includes robust error handling for:
- Database connection failures
- Missing robot data
- Invalid spatial metrics
- Slack API errors
- Individual robot processing failures
- Remote.IT API connection issues

The service continues running even if individual robots fail to process, ensuring overall system reliability. 

