import json
import logging
import time
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>

import psycopg
from slack_sdk import WebClient

from visualization_requests import request_visualization
from utils import EcefCoordinate

logger = logging.getLogger(__name__)


def get_last_location(alexbot_conn: psycopg.Connection, robot_id: str) -> Optional[Tuple[EcefCoordinate, int]]:
    """Get the last known location and timestamp for a robot from alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                "SELECT ecef_x, ecef_y, ecef_z, timestamp_ms "
                "FROM last_location WHERE robot_id = %s "
                "ORDER BY timestamp_ms DESC LIMIT 1",
                (robot_id,),
            )
            result = cursor.fetchone()
            if result:
                location = EcefCoordinate(float(result[0]), float(result[1]), float(result[2]))
                timestamp_ms = result[3]
                return (location, timestamp_ms)
            return None
    except Exception as e:
        logger.error(f"Failed to get last location for robot {robot_id}: {e}")
        return None


def get_robot_id_from_serial(portal_conn: psycopg.Connection, robot_serial: str) -> Optional[int]:
    """Get robot ID from portal database using robot serial."""
    try:
        with portal_conn.cursor() as cursor:
            cursor.execute("SELECT id FROM robots WHERE serial = %s AND deleted_at IS NULL", (robot_serial,))
            result = cursor.fetchone()
            return result[0] if result else None
    except Exception as e:
        logger.error(f"Failed to get robot ID for serial {robot_serial}: {e}")
        return None


def get_latest_spatial_metrics(
    portal_conn: psycopg.Connection, robot_id: int, last_timestamp_ms: Optional[int] = None
) -> Optional[Dict]:
    """Get the latest spatial metrics for a robot from portal database with timestamp filtering."""
    try:
        # Calculate current time in milliseconds
        current_time_ms = int(time.time() * 1000)

        # Calculate 15 minutes into the future (15 * 60 * 1000 = 900000 ms)
        future_limit_ms = current_time_ms + (15 * 60 * 1000)

        with portal_conn.cursor() as cursor:
            if last_timestamp_ms is not None:
                # Filter by timestamp: greater than last timestamp but not more than 15 minutes in the future
                cursor.execute(
                    """
                    SELECT block, timestamp_ms
                    FROM spatial_metrics
                    WHERE robot_id = %s
                      AND deleted_at IS NULL
                      AND timestamp_ms > %s
                      AND timestamp_ms <= %s
                    ORDER BY timestamp_ms DESC
                    LIMIT 1
                    """,
                    (robot_id, last_timestamp_ms, future_limit_ms),
                )
            else:
                # No last timestamp, just filter by future limit
                cursor.execute(
                    """
                    SELECT block, timestamp_ms
                    FROM spatial_metrics
                    WHERE robot_id = %s
                      AND deleted_at IS NULL
                      AND timestamp_ms <= %s
                    ORDER BY timestamp_ms DESC
                    LIMIT 1
                    """,
                    (robot_id, future_limit_ms),
                )

            result = cursor.fetchone()
            if result:
                block_data = json.loads(result[0]) if isinstance(result[0], str) else result[0]
                return {"block": block_data, "timestamp_ms": result[1]}
            return None
    except Exception as e:
        logger.error(f"Failed to get spatial metrics for robot {robot_id}: {e}")
        return None


def extract_ecef_location(spatial_data: Dict) -> Optional[EcefCoordinate]:
    """Extract ECEF coordinates from spatial metrics block."""
    try:
        # Get the end position from the block
        end_data = spatial_data.get("end", {})
        ecef_x = end_data.get("ecef_x")
        ecef_y = end_data.get("ecef_y")
        ecef_z = end_data.get("ecef_z")

        if ecef_x is not None and ecef_y is not None and ecef_z is not None:
            return EcefCoordinate(float(ecef_x), float(ecef_y), float(ecef_z))
        return None
    except Exception as e:
        logger.error(f"Failed to extract ECEF location: {e}")
        return None


def update_last_location(alexbot_conn: psycopg.Connection, robot_id: str, location: EcefCoordinate, timestamp_ms: int):
    """Update the last location for a robot in alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                INSERT INTO last_location (robot_id, timestamp_ms, ecef_x, ecef_y, ecef_z)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (robot_id) DO UPDATE SET
                    timestamp_ms = EXCLUDED.timestamp_ms,
                    ecef_x = EXCLUDED.ecef_x,
                    ecef_y = EXCLUDED.ecef_y,
                    ecef_z = EXCLUDED.ecef_z
                """,
                (robot_id, timestamp_ms, location.x, location.y, location.z),
            )
            alexbot_conn.commit()
            logger.info(f"Updated last location for robot {robot_id}")
    except Exception as e:
        logger.error(f"Failed to update last location for robot {robot_id}: {e}")
        alexbot_conn.rollback()


def send_slack_notification(
    client: WebClient,
    robot_id: str,
    distance: float,
    old_location: EcefCoordinate,
    new_location: EcefCoordinate,
    channel: str,
):
    """Send Slack notification about significant location change to the specified channel."""
    try:
        message = (
            f"🚨 *New Field Alert: {robot_id}* 🚨\n"
            f"Distance Moved: *{distance:.0f}* meters\n\n"
            f"<https://customer.cloud.carbonrobotics.com/fleet/robots/{robot_id}|View in Portal>"
        )

        response = client.chat_postMessage(channel=channel, text=message)
        logger.info(f"Sent Slack notification for robot {robot_id} to channel {channel}: {response}")
    except Exception as e:
        logger.error(f"Failed to send Slack notification to channel {channel}: {e}")


def check_robot_movement(
    robot_serial: str,
    support_channel: str,
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    slack_client: WebClient,
    distance_threshold: float,
):
    """Check if a specific robot has moved significantly and send notification if needed."""
    try:
        # Get robot ID from portal database
        robot_id = get_robot_id_from_serial(portal_conn, robot_serial)
        if not robot_id:
            logger.warning(f"Robot with serial {robot_serial} not found in portal database")
            return

        # Get last known location and timestamp
        last_location_data = get_last_location(alexbot_conn, robot_serial)
        last_timestamp_ms = None
        last_location = None

        if last_location_data:
            last_location, last_timestamp_ms = last_location_data

        # Get latest spatial metrics with timestamp filtering
        spatial_data = get_latest_spatial_metrics(portal_conn, robot_id, last_timestamp_ms)
        if not spatial_data:
            logger.warning(f"No new spatial data found for robot {robot_serial}")
            return

        # Extract current ECEF location
        current_location = extract_ecef_location(spatial_data["block"])
        if not current_location:
            logger.warning(f"Could not extract ECEF location for robot {robot_serial}")
            return

        if last_location:
            # Calculate distance and check if it exceeds threshold
            distance = current_location.distance_to(last_location)
            logger.info(f"Robot {robot_serial} moved {distance:.2f} meters")

            # Only send alarm if distance exceeds threshold
            if distance > distance_threshold:
                logger.warning(f"Robot {robot_serial} moved {distance:.2f} meters (threshold: {distance_threshold})")
                send_slack_notification(
                    slack_client, robot_serial, distance, last_location, current_location, support_channel
                )
                request_visualization(alexbot_conn, robot_serial, support_channel)
        else:
            # First time seeing this robot - just set baseline location without alarm
            logger.info(f"Setting baseline location for robot {robot_serial} at {current_location}")

        # Always update the last location (whether it's first time or not)
        update_last_location(alexbot_conn, robot_serial, current_location, spatial_data["timestamp_ms"])

    except Exception as e:
        logger.error(f"Error processing robot {robot_serial}: {e}")
