import logging
import tempfile
from datetime import datetime, timedelta

import grpc
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from slack_sdk import WebClient

from generated.frontend.banding_pb2 import (  # type: ignore[attr-defined]
    VisualizationTypeToInclude,
    GetNextVisualizationDataForAllRowsRequest,
    GetDimensionsRequest,
)
from generated.frontend.banding_pb2_grpc import BandingServiceStub
from generated.frontend.dashboard_pb2_grpc import DashboardServiceStub
from generated.thinning.thinning_pb2 import Box, SizedNotSoGreedyCfg  # type: ignore[attr-defined]
from generated.frontend.thinning_pb2_grpc import ThinningServiceStub
from generated.weed_tracking.weed_tracking_pb2 import ThinningState, DuplicateStatus  # type: ignore[attr-defined]
from generated.util.util_pb2 import Timestamp  # type: ignore[attr-defined]

from config import VISUALIZATION_WEEDING_REQUIREMENT_MINUTES
from utils import mm_to_inch, get_prometheus_connection, get_metric_data
from remoteit import RemoteItAPI

logger = logging.getLogger(__name__)


def send_visualization_to_slack(slack_client: WebClient, image_path: str, robot_id: str, channel: str = "#testing"):
    """Send the visualization image to Slack as a message with attachment."""
    try:
        # Lookup channel ID by channel name
        try:
            # Get list of all channels
            channels_response = slack_client.conversations_list(types="public_channel", limit=1000)
            if not channels_response["ok"]:
                logger.error(f"Failed to get channels list: {channels_response}")
                return False

            channel_id = None
            for ch in channels_response["channels"]:
                if ch["name"] == channel.lstrip("#"):
                    channel_id = ch["id"]
                    logger.info(f"Found channel ID for {channel}: {channel_id}")
                    break

            if not channel_id:
                logger.error(
                    f"Channel {channel} not found in workspace. "
                    f"Available channels: {[ch['name'] for ch in channels_response['channels']]}"
                )
                return False

        except Exception as e:
            logger.error(f"Failed to lookup channel ID for {channel}: {e}")
            return False

        # Join channel if not already a member
        try:
            channel_info = slack_client.conversations_info(channel=channel_id)
            is_member = channel_info["channel"].get("is_member", False)

            if not is_member:
                try:
                    join_response = slack_client.conversations_join(channel=channel_id)
                    logger.info(f"Successfully joined channel {channel} (ID: {channel_id}): {join_response}")
                except Exception as join_error:
                    logger.error(f"Failed to join channel {channel} (ID: {channel_id}): {join_error}")
                    return False
        except Exception as e:
            logger.error(f"Could not check membership for channel {channel} (ID: {channel_id}): {e}")
            return False

        # Upload file directly to the channel
        response = slack_client.files_upload_v2(
            channel=channel_id,
            file=image_path,
            filename=f"{robot_id}_visualization.png",
            initial_comment=(
                f":carbon-rooster-lasers: *Visualization for {robot_id}* :carbon-rooster-lasers:\n"
                f"Legend: :large_green_circle: Crop, :red_circle: Weed, :large_purple_circle: Kept crop, "
                f":large_yellow_square: Band, :large_purple_square: Kill Box, "
                f":negative_squared_cross_mark: Marked for thinning"
            ),
        )

        if response["ok"]:
            logger.info(
                f"Successfully uploaded visualization to Slack channel {channel} (ID: {channel_id}): {response}"
            )
            return True
        else:
            logger.error(f"Failed to upload file: {response}")
            return False

    except Exception as e:
        logger.error(f"Failed to send visualization to Slack: {e}")
        return False


def plot_row(snapshot, thinning_conf, ax, x_offset=0, y_offset=0):
    # Draw bands (yellow rectangles at the top of each block)
    for band in snapshot.bands:
        x = mm_to_inch(band.offset_mm) + x_offset
        width = mm_to_inch(band.width_mm)
        rect = patches.Rectangle(
            (x - width / 2, 0), width, 1000, linewidth=1, edgecolor="yellow", facecolor="yellow", alpha=0.7
        )
        ax.add_patch(rect)

    # Draw killBoxes (main pink blocks)
    for box in snapshot.kill_boxes:
        x0 = mm_to_inch(box.top_left.x) + x_offset
        y0 = mm_to_inch(box.top_left.y) + y_offset
        x1 = mm_to_inch(box.bottom_right.x) + x_offset
        y1 = mm_to_inch(box.bottom_right.y) + y_offset
        rect = patches.Rectangle(
            (x0, y0), x1 - x0, y1 - y0, linewidth=1, edgecolor="black", facecolor="pink", alpha=0.5
        )
        ax.add_patch(rect)

    thinning_box = None
    if thinning_conf is not None:
        if thinning_conf.box != Box():
            thinning_box = thinning_conf.box
        elif thinning_conf.sized_cfg != SizedNotSoGreedyCfg():
            thinning_box = thinning_conf.sized_cfg.min_keepout

    # Generate keepout boxes
    keepout_boxes = []
    if thinning_box is not None:
        for traj in snapshot.trajectories:
            x = mm_to_inch(traj.x_mm) + x_offset
            y = mm_to_inch(traj.y_mm) + y_offset
            if traj.thinning_state == ThinningState.THINNING_KEPT:
                keepout_boxes.append(
                    (x - thinning_box.height / 2, y - thinning_box.width, x + thinning_box.height / 2, y)
                )

    # Draw trajectories (plants, weeds, crops)
    for traj in snapshot.trajectories:
        x = mm_to_inch(traj.x_mm) + x_offset
        y = mm_to_inch(traj.y_mm) + y_offset
        r = mm_to_inch(traj.radius_mm)
        if traj.thinning_state == ThinningState.THINNING_KEPT:
            ax.plot(x, y, "o", color="purple", markersize=r * 4, alpha=0.8)
        elif traj.thinning_state == ThinningState.THINNING_MARKED_FOR_THINNING:
            ax.plot(x, y, "x", color="green", markersize=r * 4, alpha=0.8)
        elif traj.category == "CROP":
            color = "gray" if traj.duplicate_status == DuplicateStatus.DUPLICATE else "green"
            ax.plot(x, y, "o", color=color, markersize=r * 4, alpha=0.8)
        elif not (traj.duplicate_status == DuplicateStatus.DUPLICATE) and not traj.out_of_band:
            if any(x0 <= x <= x1 and y0 <= y <= y1 for x0, y0, x1, y1 in keepout_boxes):
                ax.plot(x, y, "o", color="red", markersize=r * 4, alpha=0.2)
            else:
                ax.plot(x, y, "o", color="red", markersize=r * 4, alpha=0.8)


def check_weeding_requirement(robot_id: str) -> bool:
    """
    Check if robot has been weeding continuously for the configured duration.
    Returns True if weeding requirement is met, False otherwise.
    """
    try:
        prom = get_prometheus_connection()

        # Calculate time range (configured weeding requirement window)
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=VISUALIZATION_WEEDING_REQUIREMENT_MINUTES)

        # Get weeding status data
        weeding_data = get_metric_data(prom, "commander_is_weeding", robot_id, start_time, end_time)

        if not weeding_data:
            logger.info(f"No weeding data found for robot {robot_id}")
            return False

        # Check if robot was weeding continuously for the entire time window
        all_weeding = True

        for data_point in weeding_data:
            for timestamp, value in data_point.get("values", []):
                if value != "1":
                    all_weeding = False
                    break
            if not all_weeding:
                break

        if not all_weeding:
            logger.info(
                f"Robot {robot_id} was not weeding continuously for the past "
                f"{VISUALIZATION_WEEDING_REQUIREMENT_MINUTES} minutes"
            )
            return False

        logger.info(
            f"Robot {robot_id} has been weeding continuously for the past "
            f"{VISUALIZATION_WEEDING_REQUIREMENT_MINUTES} minutes"
        )
        return True

    except Exception as e:
        logger.error(f"Error checking weeding requirement for robot {robot_id}: {e}")
        return False


def make_visualization_and_send_to_slack(
    remoteit_client: RemoteItAPI, slack_client: WebClient, robot_id: str, slack_channel: str
) -> bool:
    """
    Create and send visualization to Slack. Returns True if successful, False otherwise.
    """
    # Check weeding requirement before proceeding
    if not check_weeding_requirement(robot_id):
        logger.info(f"Visualization skipped for {robot_id}: weeding requirement not met")
        return False

    logger.info(f"Connecting to {robot_id} via remote.it...")
    connection_result = remoteit_client.connect_to_device(f"{robot_id}-command", "Frontend API (for Carbon App)")

    if not connection_result:
        logger.error("Failed to connect to robot via remote.it")
        return False

    session_id, host, port = connection_result
    logger.info(f"Connected to robot: {host}:{port}")

    try:
        # Create gRPC channel using the remote.it connection
        channel = grpc.insecure_channel(f"{host}:{port}")
        dashboard_stub = DashboardServiceStub(channel)
        banding_stub = BandingServiceStub(channel)
        thinning_stub = ThinningServiceStub(channel)

        dash_state = dashboard_stub.GetNextDashboardState(Timestamp(timestamp_ms=0))
        row_spacing = dash_state.row_width_in
        weeding_enabled = dash_state.weeding_enabled

        if not weeding_enabled:
            logger.info(f"Weeding is disabled for {robot_id}, skipping visualization")
            return False

        viz = banding_stub.GetNextVisualizationDataForAllRows(
            GetNextVisualizationDataForAllRowsRequest(
                ts=Timestamp(timestamp_ms=0), types_to_include=VisualizationTypeToInclude.values()
            )
        )
        snapshots = list(viz.data_per_row.values())

        num_rows = len(snapshots)
        row_dims = [banding_stub.GetDimensions(GetDimensionsRequest(row_id=idx + 1)) for idx in range(num_rows)]

        thinning_conf = thinning_stub.GetNextConfigurations(Timestamp(timestamp_ms=0))
        active_thinning_conf = None
        for conf in thinning_conf.definitions:
            if conf.id == thinning_conf.active_id:
                active_thinning_conf = conf
                break

        fig, ax = plt.subplots(figsize=np.array([num_rows * row_spacing, 140]) / 20)

        # Plot rows
        for idx, snapshot in enumerate(snapshots):
            row_thinning_conf = None
            if active_thinning_conf is not None:
                row_thinning_conf = active_thinning_conf.rows[idx + 1]
            x_offset = idx * row_spacing - mm_to_inch(row_dims[idx].min_x_mm)
            y_offset = -mm_to_inch(row_dims[idx].min_y_mm)
            plot_row(snapshot, row_thinning_conf, ax, x_offset=x_offset, y_offset=y_offset)

        # Place row labels at the center of each subchart
        for idx in range(num_rows):
            ax.text((idx + 0.5) * row_spacing, -5, f"Row {idx+1}", fontsize=12, ha="center", va="center")

        ax.set_aspect("equal")
        ax.set_xlabel("X (inches)")
        ax.set_ylabel("Y (inches)")
        ax.set_title(f"{robot_id}: Visualization for All Rows")
        ax.grid(True, which="both", linestyle="--", linewidth=0.5, alpha=0.5)
        ax.set_xlim(-10, num_rows * row_spacing + 10)
        ax.set_ylim(-10, 130)
        ax.invert_yaxis()
        plt.tight_layout()

        with tempfile.NamedTemporaryFile(suffix=".png") as temp_file:
            plt.savefig(temp_file.name, dpi=200)
            success = send_visualization_to_slack(slack_client, temp_file.name, robot_id, slack_channel)
            if success:
                logger.info("Visualization sent to Slack successfully")
            else:
                logger.error("Failed to send visualization to Slack")
            plt.close()
            return success

    except Exception as e:
        logger.error(f"Error during visualization: {e}")
        return False

    finally:
        # Disconnect the remote.it session
        logger.info(f"Disconnecting from {robot_id}...")
        if remoteit_client.disconnect_session(session_id):
            logger.info(f"Successfully disconnected from {robot_id}")
        else:
            logger.error(f"Failed to disconnect from {robot_id}")
