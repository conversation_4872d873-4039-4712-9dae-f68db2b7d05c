"""
Remote.IT API Integration Module

This module provides functionality to interact with Remote.IT's GraphQL API
for managing remote connections to provisioned devices.

Based on: https://docs.remote.it/developer-tools/api
"""

import json
import logging
import os
from base64 import b64decode
from typing import Dict, List, Optional, Tuple
import requests
from requests_http_signature import HTTPSignatureAuth, algorithms
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# URL Constants
REMOTEIT_URL = "https://api.remote.it/graphql/v1"


@dataclass
class RemoteItDevice:
    """Represents a Remote.IT device."""

    device_id: str
    name: str
    services: List["RemoteItService"]
    online: bool = False


@dataclass
class RemoteItService:
    """Represents a service on a Remote.IT device."""

    service_id: str
    name: str
    type: str
    port: int
    host: str
    enabled: bool = True


class RemoteItAPI:
    """Remote.IT API client for managing remote connections."""

    def __init__(self):
        """
        Initialize the Remote.IT API client.
        """
        self.auth = self._get_authorizer()
        self.session = requests.Session()

    def _get_authorizer(self) -> HTTPSignatureAuth:
        """Authorize remote.it API access using environment variables.

        Returns:
            HTTPSignatureAuth: Auth object to be used in requests.
        """
        # Get credentials from environment variables
        key_id = os.getenv("R3_ACCESS_KEY_ID")
        key = os.getenv("R3_SECRET_ACCESS_KEY")

        if not key_id or not key:
            raise Exception("Missing required environment variables: R3_ACCESS_KEY_ID and R3_SECRET_ACCESS_KEY")

        # Create and return auth object
        return HTTPSignatureAuth(key_id=key_id, key=b64decode(key), signature_algorithm=algorithms.HMAC_SHA256)

    def _make_graphql_request(self, query: str, variables: Optional[Dict] = None) -> Optional[Dict]:
        """Send a GraphQL query to remote.it API.

        Args:
            query: GraphQL query string.
            variables: Variables for the GraphQL query.

        Returns:
            dict: Parsed JSON response from the API.
        """
        try:
            response = requests.post(REMOTEIT_URL, json={"query": query, "variables": variables}, auth=self.auth)
            if response.status_code != 200:
                logger.error(f"remote.it API error: {response.status_code}")
                return None
            return json.loads(response.text)
        except Exception as e:
            logger.error(f"GraphQL request failed: {e}")
            return None

    def get_devices(self, size: int = 1000, from_index: int = 0, sort: str = "name") -> List[RemoteItDevice]:
        """
        Get all devices from your Remote.IT account using the latest API structure.
        Supports pagination.
        """
        query = """
        query getDevices($size: Int, $from: Int, $sort: String) {
          login {
            devices(size: $size, from: $from, sort: $sort) {
              total
              hasMore
              items {
                id
                name
                hardwareId
                online
                services {
                  id
                  name
                  type
                  port
                  host
                  enabled
                }
              }
            }
          }
        }
        """
        variables = {"size": size, "from": from_index, "sort": sort}
        response = self._make_graphql_request(query, variables)
        if not response or "data" not in response or "login" not in response["data"]:
            logger.error(f"Failed to get devices from Remote.IT API: {response}")
            return []
        device_items = response["data"]["login"]["devices"]["items"]
        devices = []
        for device_data in device_items:
            services = []
            for service_data in device_data.get("services", []):
                services.append(
                    RemoteItService(
                        service_id=service_data["id"],
                        name=service_data["name"],
                        type=service_data.get("type", ""),
                        port=service_data.get("port", 0),
                        host=service_data.get("host", ""),
                        enabled=service_data.get("enabled", True),
                    )
                )
            devices.append(
                RemoteItDevice(
                    device_id=device_data["id"],
                    name=device_data["name"],
                    services=services,
                    online=device_data.get("online", False),
                )
            )
        return devices

    def get_device_by_id(self, device_id: str) -> Optional[RemoteItDevice]:
        """
        Get a specific device by ID using the latest API structure.
        """
        query = """
        query getDevices($id: String) {
          login {
            devices(id: $id) {
              items {
                id
                name
                hardwareId
                online
                services {
                  id
                  name
                  type
                  port
                  host
                  enabled
                }
              }
            }
          }
        }
        """
        variables = {"id": device_id}
        response = self._make_graphql_request(query, variables)
        if not response or "data" not in response or "login" not in response["data"]:
            logger.error(
                f"Failed to get device {device_id} from Remote.IT API: "
                f"{response.get('errors') if response else 'No response'}"
            )
            return None
        items = response["data"]["login"]["devices"]["items"]
        if not items:
            return None
        device_data = items[0]
        services = []
        for service_data in device_data.get("services", []):
            services.append(
                RemoteItService(
                    service_id=service_data["id"],
                    name=service_data["name"],
                    type=service_data.get("type", ""),
                    port=service_data.get("port", 0),
                    host=service_data.get("host", ""),
                    enabled=service_data.get("enabled", True),
                )
            )
        return RemoteItDevice(
            device_id=device_data["id"],
            name=device_data["name"],
            services=services,
            online=device_data.get("online", False),
        )

    def connect_to_service(self, service_id: str) -> Optional[Tuple[str, str, int]]:
        """
        Connect to a remote.it service and return session details.

        Args:
            service_id: The service ID.

        Returns:
            tuple: Session ID, host, and port, or None if failed.
        """
        query = "mutation connect($id: String!) {connect(serviceId: $id) {id host port}}"
        response = self._make_graphql_request(query, variables={"id": service_id})

        if not response or "data" not in response:
            logger.error(f"Failed to connect to service {service_id}")
            return None

        connection = response["data"]["connect"]
        return connection["id"], connection["host"], connection["port"]

    def disconnect_session(self, session_id: str) -> bool:
        """
        Disconnect a remote.it session.

        Args:
            session_id: The session ID to disconnect.

        Returns:
            bool: True if successful, False otherwise.
        """
        query = "mutation disconnect($id: String!) {disconnect(connectionId: $id)}"
        response = self._make_graphql_request(query, variables={"id": session_id})

        if not response or "data" not in response:
            logger.error(f"Failed to disconnect session {session_id}")
            return False

        return response["data"]["disconnect"]

    def find_device_by_name(self, device_name: str) -> Optional[RemoteItDevice]:
        """
        Find a device by name using the latest API structure.
        """
        query = """
        query getDevices($name: String) {
          login {
            devices(name: $name) {
              items {
                id
                name
                hardwareId
                online
                services {
                  id
                  name
                  type
                  port
                  host
                  enabled
                }
              }
            }
          }
        }
        """
        variables = {"name": device_name}
        response = self._make_graphql_request(query, variables)
        if not response or "data" not in response or "login" not in response["data"]:
            logger.error(
                f"Failed to find device by name {device_name} from Remote.IT API: "
                f"{response.get('errors') if response else 'No response'}"
            )
            return None
        items = response["data"]["login"]["devices"]["items"]
        for device_data in items:
            if device_data["name"].lower() == device_name.lower():
                services = []
                for service_data in device_data.get("services", []):
                    services.append(
                        RemoteItService(
                            service_id=service_data["id"],
                            name=service_data["name"],
                            type=service_data.get("type", ""),
                            port=service_data.get("port", 0),
                            host=service_data.get("host", ""),
                            enabled=service_data.get("enabled", True),
                        )
                    )
                return RemoteItDevice(
                    device_id=device_data["id"],
                    name=device_data["name"],
                    services=services,
                    online=device_data.get("online", False),
                )
        return None

    def connect_to_device(self, device_name: str, service_name: str) -> Optional[Tuple[str, str, int]]:
        """
        Connect to a device by name and service type.

        Args:
            device_name: Name of the device to connect to
            service_name: Name of the service to connect to

        Returns:
            Tuple of (session_id, host, port) or None if failed
        """
        # Find device by name
        device = self.find_device_by_name(device_name)
        if not device:
            logger.error(f"Device '{device_name}' not found")
            return None

        # Check if device is online
        if not device.online:
            logger.error(f"Device '{device_name}' is offline")
            return None

        # Find service by type
        target_service = None
        for service in device.services:
            if service.name.lower() == service_name.lower() and service.enabled:
                target_service = service
                break

        if not target_service:
            logger.error(f"No {service_name} service found for device '{device_name}'")
            return None

        # Create connection
        return self.connect_to_service(target_service.service_id)
