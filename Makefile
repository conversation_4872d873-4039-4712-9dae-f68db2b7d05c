IMAGE_NAME = alexbot
LINT_IMAGE_NAME = alexbot.lint
PROTOS_IMAGE_NAME = alexbot.protos

.PHONY: build run dev clean lint protos

build:
	docker build -t $(IMAGE_NAME) .

run: build
	docker run -i \
		-t \
		--rm \
		--network host \
		-e ALEXBOT_DB_URL \
		-e PORTAL_DB_URL \
		-e SLACK_TOKEN \
		-e SLACK_APP_TOKEN \
		-e PROMETHEUS_URL \
		-e ROSY_URL \
		-e R3_ACCESS_KEY_ID \
		-e R3_SECRET_ACCESS_KEY \
		$(IMAGE_NAME)

dev: build
	docker run -i \
		-t \
		--rm \
		--network host \
		-e ALEXBOT_DB_URL \
		-e PORTAL_DB_URL \
		-e SLACK_TOKEN \
		-e SLACK_APP_TOKEN \
		-e SPEED_DIFFERENCE_THRESHOLD_MPH \
		-e SPEED_MONITORING_WINDOW_MINUTES \
		-e SPEED_ALERT_INTERVAL_MINUTES \
		-e SPEED_CONSTANCY_TOLERANCE_MPH \
		-e SPEED_CONSTANCY_WINDOW_MINUTES \
		-e SUPPORT_CHANNEL_OVERRIDE \
		-e PROMETHEUS_URL \
		-e ROSY_URL \
		-e DISTANCE_THRESHOLD_METERS \
		-e MONITORING_INTERVAL_SECONDS \
		-e MAX_THINNING_BOX_THRESHOLD_INCHES \
		-e THINNING_BOX_ALERT_INTERVAL_MINUTES \
		-e R3_ACCESS_KEY_ID \
		-e R3_SECRET_ACCESS_KEY \
		-e VISUALIZATION_RATE_LIMIT_MINUTES \
		-e VISUALIZATION_WEEDING_REQUIREMENT_MINUTES \
		-e LOG_LEVEL \
		-v .:/alexbot \
		$(IMAGE_NAME)

clean:
	docker rmi -f $(IMAGE_NAME)

lint: build
	docker build -t $(LINT_IMAGE_NAME) -f Dockerfile.lint .
	docker run -it --rm -v .:/app $(LINT_IMAGE_NAME)

protos: build
	docker build -t $(PROTOS_IMAGE_NAME) -f Dockerfile.protos .
	docker run -it --rm -v .:/app $(PROTOS_IMAGE_NAME)

