import logging
import time
from typing import Dict, Optional, <PERSON><PERSON>

import psycopg
from slack_sdk import WebClient

from .robot_movement import get_robot_id_from_serial
from .visualization_requests import request_visualization

logger = logging.getLogger(__name__)


def get_latest_crop_model(
    portal_conn: psycopg.Connection, robot_id: int, last_timestamp_ms: Optional[int] = None
) -> Optional[Dict]:
    """Get the latest crop model for a robot from portal database with timestamp filtering."""
    try:
        # Calculate current time in milliseconds
        current_time_ms = int(time.time() * 1000)

        # Calculate 15 minutes into the future (15 * 60 * 1000 = 900000 ms)
        future_limit_ms = current_time_ms + (15 * 60 * 1000)

        with portal_conn.cursor() as cursor:
            # Convert ms to seconds for comparison
            last_timestamp_s = last_timestamp_ms // 1000 if last_timestamp_ms is not None else None
            future_limit_s = future_limit_ms // 1000
            if last_timestamp_ms is not None:
                # Filter by timestamp: greater than last timestamp but not more than 15 minutes in the future
                cursor.execute(
                    """
                    SELECT model, reported_at
                    FROM health_logs
                    WHERE robot_id = %s
                      AND model IS NOT NULL
                      AND reported_at > %s
                      AND reported_at <= %s
                    ORDER BY reported_at DESC
                    LIMIT 1
                    """,
                    (robot_id, last_timestamp_s, future_limit_s),
                )
            else:
                # No last timestamp, just filter by future limit
                cursor.execute(
                    """
                    SELECT model, reported_at
                    FROM health_logs
                    WHERE robot_id = %s
                      AND model IS NOT NULL
                      AND reported_at <= %s
                    ORDER BY reported_at DESC
                    LIMIT 1
                    """,
                    (robot_id, future_limit_s),
                )

            result = cursor.fetchone()
            if result:
                model = result[0]
                # Convert reported_at (seconds) to ms for consistency
                timestamp_ms = int(result[1]) * 1000
                return {"model": model, "timestamp_ms": timestamp_ms}
            return None
    except Exception as e:
        logger.error(f"Failed to get crop model for robot {robot_id}: {e}")
        return None


def extract_model_type(model: str) -> str:
    """Extract model type from model string (e.g., 'prt' from 'prt-20250522-qmyl2406pr')."""
    if not model or "-" not in model:
        return model
    return model.split("-")[0]


def get_last_crop_model(alexbot_conn: psycopg.Connection, robot_id: str) -> Optional[Tuple[str, int]]:
    """Get the last known crop model and timestamp for a robot from alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                "SELECT model, timestamp_ms "
                "FROM last_crop_model WHERE robot_id = %s "
                "ORDER BY timestamp_ms DESC LIMIT 1",
                (robot_id,),
            )
            result = cursor.fetchone()
            if result:
                return (result[0], result[1])
            return None
    except Exception as e:
        logger.error(f"Failed to get last crop model for robot {robot_id}: {e}")
        return None


def update_last_crop_model(alexbot_conn: psycopg.Connection, robot_id: str, model: str, timestamp_ms: int):
    """Update the last crop model for a robot in alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                INSERT INTO last_crop_model (robot_id, timestamp_ms, model)
                VALUES (%s, %s, %s)
                ON CONFLICT (robot_id) DO UPDATE SET
                    timestamp_ms = EXCLUDED.timestamp_ms,
                    model = EXCLUDED.model
                """,
                (robot_id, timestamp_ms, model),
            )
            alexbot_conn.commit()
            logger.info(f"Updated last crop model for robot {robot_id}")
    except Exception as e:
        logger.error(f"Failed to update last crop model for robot {robot_id}: {e}")
        alexbot_conn.rollback()


def send_crop_model_notification(
    client: WebClient,
    robot_id: str,
    old_model: str,
    new_model: str,
    old_model_type: str,
    new_model_type: str,
    channel: str,
):
    """Send Slack notification about crop model change to the specified channel."""
    try:
        message = (
            f"🤖 *Crop Model Type Change Alert: {robot_id}* 🤖\n"
            f"Old Model: *{old_model}* (Type: {old_model_type})\n"
            f"New Model: *{new_model}* (Type: {new_model_type})\n\n"
            f"<https://customer.cloud.carbonrobotics.com/fleet/robots/{robot_id}|View in Portal>"
        )

        response = client.chat_postMessage(channel=channel, text=message)
        logger.info(f"Sent crop model notification for robot {robot_id} to channel {channel}: {response}")
    except Exception as e:
        logger.error(f"Failed to send crop model notification to channel {channel}: {e}")


def monitor_crop_model(
    robot_serial: str,
    support_channel: str,
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    slack_client: WebClient,
):
    """Check if a specific robot's crop model has changed and send notification if needed."""
    try:
        # Get robot ID from portal database
        robot_id = get_robot_id_from_serial(portal_conn, robot_serial)
        if not robot_id:
            logger.warning(f"Robot with serial {robot_serial} not found in portal database")
            return

        # Get last known crop model and timestamp
        last_model_data = get_last_crop_model(alexbot_conn, robot_serial)
        last_timestamp_ms = None
        last_model = None

        if last_model_data:
            last_model, last_timestamp_ms = last_model_data

        # Get latest crop model with timestamp filtering
        model_data = get_latest_crop_model(portal_conn, robot_id, last_timestamp_ms)
        if not model_data:
            logger.warning(f"No new crop model data found for robot {robot_serial}")
            return

        current_model = model_data["model"]
        current_model_type = extract_model_type(current_model)

        if not current_model:
            logger.warning(f"No model found for robot {robot_serial}")
            return

        if last_model:
            last_model_type = extract_model_type(last_model)

            # Check if model type has changed
            if current_model_type != last_model_type:
                logger.warning(
                    f"Crop model type changed for robot {robot_serial}: "
                    f"{last_model_type} ({last_model}) -> {current_model_type} ({current_model})"
                )
                send_crop_model_notification(
                    slack_client,
                    robot_serial,
                    last_model,
                    current_model,
                    last_model_type,
                    current_model_type,
                    support_channel,
                )
                request_visualization(alexbot_conn, robot_serial, support_channel)
            else:
                logger.info(f"Crop model type unchanged for robot {robot_serial}: {current_model_type}")
        else:
            # First time seeing this robot - just set baseline model without alarm
            logger.info(f"Setting baseline crop model for robot {robot_serial}: {current_model}")

        # Always update the last crop model (whether it's first time or not)
        update_last_crop_model(alexbot_conn, robot_serial, current_model, model_data["timestamp_ms"])

    except Exception as e:
        logger.error(f"Error processing crop model for robot {robot_serial}: {e}")
