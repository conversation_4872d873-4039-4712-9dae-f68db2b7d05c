import logging
import psycopg
import time
from typing import Op<PERSON>, List, Tuple
from .config import VISUALIZATION_RATE_LIMIT_MINUTES

logger = logging.getLogger(__name__)


def request_visualization(
    alexbot_conn: psycopg.Connection,
    robot_id: str,
    channel: str,
    requested_by: Optional[str] = None,
    bypass_rate_limit: bool = False,
) -> bool:
    """
    Request a visualization for a robot. Returns True if request was made, False if rate limited.

    Args:
        alexbot_conn: Database connection
        robot_id: Robot ID to request visualization for
        channel: Slack channel to send visualization to
        requested_by: Username who requested the visualization (optional)
        bypass_rate_limit: If True, skip rate limiting checks
    """
    try:
        current_timestamp_ms = int(time.time() * 1000)

        # Check if we can request a visualization (rate limiting)
        if not bypass_rate_limit:
            last_request = get_last_visualization_request(alexbot_conn, robot_id)

            if last_request:
                last_completed_time = last_request["completed_timestamp_ms"]

                # If the last request was completed, check the time since completion
                if last_completed_time is not None:
                    time_since_last_completion_ms = current_timestamp_ms - last_completed_time
                    rate_limit_ms = VISUALIZATION_RATE_LIMIT_MINUTES * 60 * 1000

                    if time_since_last_completion_ms < rate_limit_ms:
                        logger.info(
                            f"Visualization request for robot {robot_id} rate limited. "
                            f"Last visualization completed {time_since_last_completion_ms / 1000 / 60:.1f} minutes ago "
                            f"(limit: {VISUALIZATION_RATE_LIMIT_MINUTES} minutes)"
                        )
                        return False
                # If the last request is still pending (completed_timestamp_ms is NULL), don't make a new request
                else:
                    logger.info(
                        f"Visualization request for robot {robot_id} skipped. Previous request is still pending."
                    )
                    return False

        # Insert or update the visualization request
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                INSERT INTO visualization_requests (
                    robot_id, requested_timestamp_ms, completed_timestamp_ms, channel, requested_by
                )
                VALUES (%s, %s, NULL, %s, %s)
                ON CONFLICT (robot_id) DO UPDATE SET
                    requested_timestamp_ms = EXCLUDED.requested_timestamp_ms,
                    completed_timestamp_ms = NULL,
                    channel = EXCLUDED.channel,
                    requested_by = EXCLUDED.requested_by
                """,
                (robot_id, current_timestamp_ms, channel, requested_by),
            )
            alexbot_conn.commit()
            logger.info(f"Visualization requested for robot {robot_id}")
            return True

    except Exception as e:
        logger.error(f"Failed to request visualization for robot {robot_id}: {e}")
        alexbot_conn.rollback()
        return False


def get_last_visualization_request(alexbot_conn: psycopg.Connection, robot_id: str) -> Optional[dict]:
    """Get the last visualization request for a robot."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT robot_id, requested_timestamp_ms, completed_timestamp_ms, channel, requested_by
                FROM visualization_requests
                WHERE robot_id = %s
                """,
                (robot_id,),
            )
            result = cursor.fetchone()

            if result:
                return {
                    "robot_id": result[0],
                    "requested_timestamp_ms": result[1],
                    "completed_timestamp_ms": result[2],
                    "channel": result[3],
                    "requested_by": result[4],
                }
            return None

    except Exception as e:
        logger.error(f"Failed to get last visualization request for robot {robot_id}: {e}")
        return None


def get_pending_visualization_requests(alexbot_conn: psycopg.Connection) -> List[Tuple[str, str, Optional[str]]]:
    """
    Get all pending visualization requests (where completed_timestamp_ms is NULL).
    Returns list of (robot_id, channel, requested_by) tuples.
    """
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT robot_id, channel, requested_by
                FROM visualization_requests
                WHERE completed_timestamp_ms IS NULL
                ORDER BY requested_timestamp_ms ASC
                """
            )
            results = cursor.fetchall()
            return [(row[0], row[1], row[2]) for row in results]

    except Exception as e:
        logger.error(f"Failed to get pending visualization requests: {e}")
        return []


def mark_visualization_completed(alexbot_conn: psycopg.Connection, robot_id: str) -> bool:
    """Mark a visualization request as completed."""
    try:
        completed_timestamp_ms = int(time.time() * 1000)

        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                UPDATE visualization_requests
                SET completed_timestamp_ms = %s
                WHERE robot_id = %s
                """,
                (completed_timestamp_ms, robot_id),
            )
            alexbot_conn.commit()
            logger.info(f"Visualization marked as completed for robot {robot_id}")
            return True

    except Exception as e:
        logger.error(f"Failed to mark visualization as completed for robot {robot_id}: {e}")
        alexbot_conn.rollback()
        return False
