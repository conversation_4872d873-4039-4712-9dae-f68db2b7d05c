import logging
import time
from typing import List, <PERSON>ple
import threading

import psycopg
from slack_sdk import WebClient

from .config import (
    ALEXBOT_DB_URL,
    PORTAL_DB_URL,
    SLACK_TOKEN,
    SUPPORT_CHANNEL_OVERRIDE,
    DISTANCE_THRESHOLD_METERS,
    MONITORING_INTERVAL_SECONDS,
    LOG_LEVEL,
    ROSY_URL,
)
from .utils import get_db_connection
from .remoteit import RemoteItAP<PERSON>
from .robot_movement import check_robot_movement
from .speed_monitoring import monitor_speed_compliance, monitor_speed_constancy
from .plant_profile_monitoring import monitor_plant_profile
from .crop_model_monitoring import monitor_crop_model
from .thinning_box_monitoring import monitor_thinning_box
from .visualization_requests import (
    get_pending_visualization_requests,
    mark_visualization_completed,
)
from .visualization import make_visualization_and_send_to_slack
from .slack_socket_mode import start_socket_mode_client


logging.basicConfig(level=getattr(logging, LOG_LEVEL))
logger = logging.getLogger(__name__)


def get_robots_to_monitor(
    alexbot_conn: psycopg.Connection,
) -> List[Tuple[str, str, bool, bool, bool, bool, bool, bool]]:
    """Get list of robot IDs, support channels, and alert enable flags from alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT
                    robot_id,
                    support_channel,
                    enable_new_field_alert,
                    enable_speed_compliance_alert,
                    enable_speed_constancy_alert,
                    enable_plant_profile_change_alert,
                    enable_crop_model_change_alert,
                    enable_thinning_box_alert
                FROM robots_to_monitor
                """
            )
            robots = [(row[0], row[1], row[2], row[3], row[4], row[5], row[6], row[7]) for row in cursor.fetchall()]
            logger.info(f"Found {len(robots)} robots to monitor")
            for (
                robot_id,
                channel,
                new_field_alert,
                speed_compliance_alert,
                speed_constancy_alert,
                plant_profile_change_alert,
                crop_model_change_alert,
                thinning_box_alert,
            ) in robots:
                logger.info(
                    f"Robot {robot_id} -> Channel {channel} | New Field: {new_field_alert} | "
                    f"Speed Compliance: {speed_compliance_alert} | Speed Constancy: {speed_constancy_alert} | "
                    f"Plant Profile Change: {plant_profile_change_alert} | "
                    f"Crop Model Change: {crop_model_change_alert} | Thinning Box: {thinning_box_alert}"
                )
            return robots
    except Exception as e:
        logger.error(f"Failed to get robots to monitor: {e}")
        return []


def monitor_robots():
    """Main monitoring function that runs every minute."""
    # Initialize Slack client
    slack_client = WebClient(token=SLACK_TOKEN)

    # Initialize RemoteIt client
    remoteit_client = RemoteItAPI()

    # Test Slack connection
    try:
        api_response = slack_client.api_test()
        logger.info(f"Slack API test successful: {api_response}")
    except Exception as e:
        logger.error(f"Slack API test failed: {e}")
        return

    while True:
        try:
            logger.info("Starting robot monitoring cycle...")

            # Connect to databases
            alexbot_conn = get_db_connection(ALEXBOT_DB_URL)
            portal_conn = get_db_connection(PORTAL_DB_URL)

            # Get robots to monitor
            robots_to_monitor = get_robots_to_monitor(alexbot_conn)

            for (
                robot_id,
                support_channel,
                enable_field_alert,
                enable_speed_alert,
                enable_constancy_alert,
                enable_profile_alert,
                enable_crop_model_alert,
                enable_thinning_box_alert,
            ) in robots_to_monitor:
                # Use override channel if set, otherwise use per-robot channel
                target_channel = SUPPORT_CHANNEL_OVERRIDE if SUPPORT_CHANNEL_OVERRIDE else support_channel

                # Check robot movement (field alert)
                if enable_field_alert:
                    check_robot_movement(
                        robot_id,
                        target_channel,
                        alexbot_conn,
                        portal_conn,
                        slack_client,
                        DISTANCE_THRESHOLD_METERS,
                    )

                # Check speed compliance
                if enable_speed_alert:
                    monitor_speed_compliance(robot_id, target_channel, alexbot_conn, slack_client)

                # Check speed constancy
                if enable_constancy_alert:
                    monitor_speed_constancy(robot_id, target_channel, alexbot_conn, slack_client)

                # Check plant profile changes
                if enable_profile_alert:
                    monitor_plant_profile(robot_id, target_channel, alexbot_conn, portal_conn, slack_client, ROSY_URL)

                # Check crop model changes
                if enable_crop_model_alert:
                    monitor_crop_model(robot_id, target_channel, alexbot_conn, portal_conn, slack_client)

                # Check thinning box size
                if enable_thinning_box_alert:
                    monitor_thinning_box(robot_id, target_channel, alexbot_conn, portal_conn, slack_client)

            # Process pending visualization requests
            pending_visualizations = get_pending_visualization_requests(alexbot_conn)
            if pending_visualizations:
                logger.info(f"Processing {len(pending_visualizations)} pending visualization requests")

                for robot_id, channel, requested_by in pending_visualizations:
                    try:
                        logger.info(f"Attempting visualization for robot {robot_id}")

                        # Attempt to create and send visualization
                        success = make_visualization_and_send_to_slack(
                            remoteit_client, slack_client, robot_id, channel, requested_by
                        )

                        if success:
                            # Mark as completed if successful
                            mark_visualization_completed(alexbot_conn, robot_id)

                    except Exception as e:
                        logger.error(f"Failed to process visualization for robot {robot_id}: {e}")
            else:
                logger.debug("No pending visualization requests")

            # Close database connections
            alexbot_conn.close()
            portal_conn.close()

            logger.info("Robot monitoring cycle completed")

        except Exception as e:
            logger.error(f"Error in monitoring cycle: {e}")

        # Wait for configured interval before next cycle
        logger.info(f"Waiting {MONITORING_INTERVAL_SECONDS} seconds before next monitoring cycle...")
        time.sleep(MONITORING_INTERVAL_SECONDS)


if __name__ == "__main__":
    logger.info("Starting AlexBot robot monitoring service...")
    # Start Slack Socket Mode client in a background thread
    socket_mode_thread = threading.Thread(target=start_socket_mode_client, daemon=True)
    socket_mode_thread.start()
    # Start main monitoring loop
    monitor_robots()
