import logging
import math
import traceback
from datetime import datetime
from typing import Optional
import psycopg
import pygeohash as pgh

from .utils import get_robot_by_channel, EcefCoordinate

logger = logging.getLogger(__name__)


def ecef_to_lat_lon(x: float, y: float, z: float) -> tuple[float, float]:
    """Convert ECEF coordinates to latitude and longitude using WGS84 ellipsoid."""
    # WGS84 ellipsoid constants
    a = 6378137.0  # Semi-major axis in meters
    e2 = 6.69437999014e-3  # First eccentricity squared

    # Calculate longitude
    lon = math.atan2(y, x)

    # Calculate latitude using iterative method
    p = math.sqrt(x*x + y*y)
    lat = math.atan2(z, p * (1 - e2))

    # Iterate to improve accuracy
    for _ in range(5):
        N = a / math.sqrt(1 - e2 * math.sin(lat)**2)
        h = p / math.cos(lat) - N
        lat = math.atan2(z, p * (1 - e2 * N / (N + h)))

    # Convert from radians to degrees
    lat_deg = math.degrees(lat)
    lon_deg = math.degrees(lon)

    return lat_deg, lon_deg


def convert_ecef_to_geohash(location_data: dict) -> Optional[str]:
    """Convert ECEF coordinates to geohash."""
    try:
        if not location_data or not all(k in location_data for k in ['x', 'y', 'z']):
            return None

        # Extract coordinates
        x = float(location_data['x'])
        y = float(location_data['y'])
        z = float(location_data['z'])

        # Convert to lat/lon
        lat, lon = ecef_to_lat_lon(x, y, z)

        # Generate geohash with precision 8 (about 38m x 19m)
        return pgh.encode(lat, lon, precision=8)

    except Exception as e:
        logger.error(f"Failed to convert ECEF to geohash: {e}")
        return None


def format_timestamp(timestamp_ms: int) -> str:
    """Format timestamp from milliseconds to human readable string."""
    try:
        # Convert milliseconds to seconds
        timestamp_s = timestamp_ms / 1000
        dt = datetime.fromtimestamp(timestamp_s)
        return dt.strftime('%Y-%m-%d %H:%M:%S UTC')
    except Exception as e:
        logger.error(f"Failed to format timestamp {timestamp_ms}: {e}")
        return "Unknown"


def validate_robot_serial(serial: str) -> bool:
    """Validate robot serial format (slayer# or reaper#)."""
    if not serial:
        return False

    serial_lower = serial.lower().strip()
    return (serial_lower.startswith("slayer") and serial_lower[6:].isdigit()) or (
        serial_lower.startswith("reaper") and serial_lower[6:].isdigit()
    )
    
def get_robot_status_from_portal(portal_conn: psycopg.Connection, robot_serial: str) -> Optional[dict]:
    """Get robot status from portal database."""

    try:
        with portal_conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT dm.created_at, dm.crop_id, dm.field_config, dm.location
                FROM daily_metrics dm
                JOIN robots r ON r.id = dm.robot_id
                WHERE LOWER(r.serial) = LOWER(%s) AND r.deleted_at IS NULL
                ORDER BY dm.created_at DESC
                LIMIT 1
                """,
                (robot_serial,),
            )
            result = cursor.fetchone()
            if result:
                return {
                    "created_at": result[0],
                    "crop_id": result[1],
                    "field_config": result[2],
                    "location": result[3],
                }
            return None
    except Exception as e:
        logger.error(f"Failed to get robot status for {robot_serial}: {e}")
        return None


def get_robot_info_from_portal(portal_conn: psycopg.Connection, robot_serial: str) -> Optional[dict]:
    """Get robot information from portal database."""
    try:
        with portal_conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT id, serial, feature_flags, software_version, implementation_status,
                       support_slack, reported_at, model
                FROM robots
                WHERE LOWER(serial) = LOWER(%s) AND deleted_at IS NULL
                """,
                (robot_serial,),
            )
            result = cursor.fetchone()
            if result:
                return {
                    "id": result[0],
                    "serial": result[1],
                    "feature_flags": result[2],
                    "software_version": result[3],
                    "implementation_status": result[4],
                    "support_slack": result[5],
                    "reported_at": result[6],
                    "model_id": result[7],
                }
            return None
    except Exception as e:
        logger.error(f"Failed to get robot info for {robot_serial}: {e}")
        return None


def format_robot_info_message(robot_info: dict, robot_status: Optional[dict], robot_serial: str, user_name: str) -> str:
    """Format robot information into a Slack message."""
    response_lines = [f"*Robot information for {robot_serial}* (requested by @{user_name})"]

    if robot_info["software_version"]:
        response_lines.append(f"• Software Version: *{robot_info['software_version']}*")

    if robot_info["model_id"]:
        response_lines.append(f"• Current Model: *{robot_info['model_id']}*")

    feature_flags = robot_info.get("feature_flags", {})

    if feature_flags is None:
        feature_flags = {}

    embedding_enabled = feature_flags.get("embedding_based_classification_feature", None)

    if embedding_enabled is None:
        classification_method = "Unknown"
    elif embedding_enabled:
        classification_method = "Plant Profiles"
    else:
        classification_method = "Thresholds"

    response_lines.append(f"• Classification Method: *{classification_method}*")

    # Add robot status information if available
    if robot_status:
        # Last check-in time
        if robot_status.get("created_at"):
            last_checkin = format_timestamp(robot_status["created_at"])
            response_lines.append(f"• Last Check-in: *{last_checkin}*")

        # Current crop ID
        if robot_status.get("crop_id"):
            response_lines.append(f"• Current Crop ID: *{robot_status['crop_id']}*")

        # Current geohash (from ECEF location)
        if robot_status.get("location"):
            geohash = convert_ecef_to_geohash(robot_status["location"])
            if geohash:
                response_lines.append(f"• Current Geohash: *{geohash}*")

        # Thinning and weeding status from field_config
        field_config = robot_status.get("field_config", {})
        if field_config:
            is_weeding = field_config.get("is_weeding", False)
            is_thinning = field_config.get("is_thinning", False)

            weeding_status = "✅ Active" if is_weeding else "❌ Inactive"
            thinning_status = "✅ Active" if is_thinning else "❌ Inactive"

            response_lines.append(f"• Weeding Status: *{weeding_status}*")
            response_lines.append(f"• Thinning Status: *{thinning_status}*")

    return "\n".join(response_lines)


def handle_info_command_logic(
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    channel_name: str,
    user_name: str,
    robot_serial: str,
) -> tuple[bool, str, Optional[str]]:

    try:
        # Determine robot serial
        if robot_serial:
            # Robot was specified as argument
            logger.info(f"User @{user_name} requested info for robot {robot_serial} in channel {channel_name}")
        else:
            # No robot specified, determine from channel

            robot_from_channel = get_robot_by_channel(portal_conn, channel_name)

            if not robot_from_channel:
                error_msg = (
                    f"❌ Error: No robot configured for channel {channel_name}. "
                    f"Please specify a robot serial: `/info <robot_serial>`"
                )
                return False, error_msg, None

            robot_serial = robot_from_channel

            logger.info(f"User @{user_name} requested info for channel {channel_name} (robot: {robot_serial})")

        # Validate robot serial format
        if not validate_robot_serial(robot_serial):
            error_msg = f"❌ Error: Invalid robot serial format '{robot_serial}'. Expected format: slayer# or reaper#"
            return False, error_msg, None

        # Get robot information from portal database
        robot_info = get_robot_info_from_portal(portal_conn, robot_serial)
        robot_status = get_robot_status_from_portal(portal_conn, robot_serial)
        if not robot_info:
            error_msg = f"❌ Error: Robot '{robot_serial}' not found in the system"
            return False, error_msg, None

        # Format the success message
        channel_message = format_robot_info_message(robot_info, robot_status, robot_serial, user_name)
        return True, channel_message, None

    except Exception as e:
        logger.error(f"Error handling /info command: {e}")
        error_msg = "❌ Error: Failed to process robot info request"
        traceback.print_exc()
        return False, error_msg, None
