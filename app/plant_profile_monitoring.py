import json
import logging
import time
from typing import Dict, Optional, <PERSON><PERSON>

import psycopg
import requests
from slack_sdk import WebClient

from .robot_movement import get_robot_id_from_serial
from .visualization_requests import request_visualization

logger = logging.getLogger(__name__)


def get_latest_plant_profile(
    portal_conn: psycopg.Connection, robot_id: int, last_timestamp_ms: Optional[int] = None
) -> Optional[Dict]:
    """Get the latest plant profile for a robot from portal database with timestamp filtering."""
    try:
        # Calculate current time in milliseconds
        current_time_ms = int(time.time() * 1000)

        # Calculate 15 minutes into the future (15 * 60 * 1000 = 900000 ms)
        future_limit_ms = current_time_ms + (15 * 60 * 1000)

        with portal_conn.cursor() as cursor:
            # Convert ms to seconds for comparison
            last_timestamp_s = last_timestamp_ms // 1000 if last_timestamp_ms is not None else None
            future_limit_s = future_limit_ms // 1000
            if last_timestamp_ms is not None:
                # Filter by timestamp: greater than last timestamp but not more than 15 minutes in the future
                cursor.execute(
                    """
                    SELECT field_config, reported_at
                    FROM health_logs
                    WHERE robot_id = %s
                      AND field_config IS NOT NULL
                      AND reported_at > %s
                      AND reported_at <= %s
                    ORDER BY reported_at DESC
                    LIMIT 1
                    """,
                    (robot_id, last_timestamp_s, future_limit_s),
                )
            else:
                # No last timestamp, just filter by future limit
                cursor.execute(
                    """
                    SELECT field_config, reported_at
                    FROM health_logs
                    WHERE robot_id = %s
                      AND field_config IS NOT NULL
                      AND reported_at <= %s
                    ORDER BY reported_at DESC
                    LIMIT 1
                    """,
                    (robot_id, future_limit_s),
                )

            result = cursor.fetchone()
            if result:
                field_config = json.loads(result[0]) if isinstance(result[0], str) else result[0]
                # Convert reported_at (seconds) to ms for consistency
                timestamp_ms = int(result[1]) * 1000
                return {"field_config": field_config, "timestamp_ms": timestamp_ms}
            return None
    except Exception as e:
        logger.error(f"Failed to get plant profile for robot {robot_id}: {e}")
        return None


def get_profile_name_from_rosy(profile_id: str, rosy_url: str) -> Optional[str]:
    """Get profile name from ROSY API."""
    try:
        url = f"{rosy_url}/internal/v1/profiles/{profile_id}?profileId={profile_id}"
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        data = response.json()
        profile_data = json.loads(data.get("profile", "{}"))
        return profile_data.get("name")
    except Exception as e:
        logger.error(f"Failed to get profile name from ROSY for {profile_id}: {e}")
        return None


def get_last_plant_profile(alexbot_conn: psycopg.Connection, robot_id: str) -> Optional[Tuple[str, str, int]]:
    """Get the last known plant profile and timestamp for a robot from alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                "SELECT profile_id, profile_name, timestamp_ms "
                "FROM last_plant_profile WHERE robot_id = %s "
                "ORDER BY timestamp_ms DESC LIMIT 1",
                (robot_id,),
            )
            result = cursor.fetchone()
            if result:
                return (result[0], result[1], result[2])
            return None
    except Exception as e:
        logger.error(f"Failed to get last plant profile for robot {robot_id}: {e}")
        return None


def update_last_plant_profile(
    alexbot_conn: psycopg.Connection, robot_id: str, profile_id: str, profile_name: str, timestamp_ms: int
):
    """Update the last plant profile for a robot in alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                INSERT INTO last_plant_profile (robot_id, timestamp_ms, profile_id, profile_name)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (robot_id) DO UPDATE SET
                    timestamp_ms = EXCLUDED.timestamp_ms,
                    profile_id = EXCLUDED.profile_id,
                    profile_name = EXCLUDED.profile_name
                """,
                (robot_id, timestamp_ms, profile_id, profile_name),
            )
            alexbot_conn.commit()
            logger.info(f"Updated last plant profile for robot {robot_id}")
    except Exception as e:
        logger.error(f"Failed to update last plant profile for robot {robot_id}: {e}")
        alexbot_conn.rollback()


def send_plant_profile_notification(
    client: WebClient,
    robot_id: str,
    old_profile_id: str,
    old_profile_name: str,
    new_profile_id: str,
    new_profile_name: str,
    channel: str,
):
    """Send Slack notification about plant profile change to the specified channel."""
    try:
        message = (
            f"🌱 *Plant Profile Change Alert: {robot_id}* 🌱\n"
            f"Old Profile: *{old_profile_name}* (`{old_profile_id}`)\n"
            f"New Profile: *{new_profile_name}* (`{new_profile_id}`)\n\n"
            f"<https://customer.cloud.carbonrobotics.com/fleet/robots/{robot_id}|View in Portal>"
        )

        response = client.chat_postMessage(channel=channel, text=message)
        logger.info(f"Sent plant profile notification for robot {robot_id} to channel {channel}: {response}")
    except Exception as e:
        logger.error(f"Failed to send plant profile notification to channel {channel}: {e}")


def monitor_plant_profile(
    robot_serial: str,
    support_channel: str,
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    slack_client: WebClient,
    rosy_url: str,
):
    """Check if a specific robot's plant profile has changed and send notification if needed."""
    try:
        # Get robot ID from portal database
        robot_id = get_robot_id_from_serial(portal_conn, robot_serial)
        if not robot_id:
            logger.warning(f"Robot with serial {robot_serial} not found in portal database")
            return

        # Get last known plant profile and timestamp
        last_profile_data = get_last_plant_profile(alexbot_conn, robot_serial)
        last_timestamp_ms = None
        last_profile_id = None
        last_profile_name = None

        if last_profile_data:
            last_profile_id, last_profile_name, last_timestamp_ms = last_profile_data

        # Get latest plant profile with timestamp filtering
        profile_data = get_latest_plant_profile(portal_conn, robot_id, last_timestamp_ms)
        if not profile_data:
            logger.warning(f"No new plant profile data found for robot {robot_serial}")
            return

        # Extract current profile ID
        field_config = profile_data["field_config"]
        current_profile_id = field_config.get("active_category_collection_id")

        if not current_profile_id:
            logger.warning(f"No active category collection ID found for robot {robot_serial}")
            return

        # Get profile name from ROSY
        current_profile_name = get_profile_name_from_rosy(current_profile_id, rosy_url)
        if not current_profile_name:
            logger.warning(f"Could not get profile name for {current_profile_id}")
            current_profile_name = "Unknown Profile"

        if last_profile_id:
            assert last_profile_name is not None

            # Check if profile has changed
            if current_profile_id != last_profile_id:
                logger.warning(
                    f"Plant profile changed for robot {robot_serial}: "
                    f"{last_profile_name} ({last_profile_id}) -> {current_profile_name} ({current_profile_id})"
                )
                send_plant_profile_notification(
                    slack_client,
                    robot_serial,
                    last_profile_id,
                    last_profile_name,
                    current_profile_id,
                    current_profile_name,
                    support_channel,
                )
                request_visualization(alexbot_conn, robot_serial, support_channel)
            else:
                logger.info(f"Plant profile unchanged for robot {robot_serial}: {current_profile_name}")
        else:
            # First time seeing this robot - just set baseline profile without alarm
            logger.info(f"Setting baseline plant profile for robot {robot_serial}: {current_profile_name}")

        # Always update the last plant profile (whether it's first time or not)
        update_last_plant_profile(
            alexbot_conn, robot_serial, current_profile_id, current_profile_name, profile_data["timestamp_ms"]
        )

    except Exception as e:
        logger.error(f"Error processing plant profile for robot {robot_serial}: {e}")
