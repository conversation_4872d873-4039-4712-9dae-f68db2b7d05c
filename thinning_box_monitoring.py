import json
import logging
import time
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>

import psycopg
from slack_sdk import WebClient

from config import MAX_THINNING_BOX_THRESHOLD_INCHES, THINNING_BOX_ALERT_INTERVAL_MINUTES
from robot_movement import get_robot_id_from_serial
from visualization_requests import request_visualization

logger = logging.getLogger(__name__)


def get_latest_thinning_config(
    portal_conn: psycopg.Connection, robot_id: int, last_timestamp_ms: Optional[int] = None
) -> Optional[Dict]:
    """Get the latest thinning configuration for a robot from portal database with timestamp filtering."""
    try:
        # Calculate current time in milliseconds
        current_time_ms = int(time.time() * 1000)

        # Calculate 15 minutes into the future (15 * 60 * 1000 = 900000 ms)
        future_limit_ms = current_time_ms + (15 * 60 * 1000)

        with portal_conn.cursor() as cursor:
            # Convert ms to seconds for comparison
            last_timestamp_s = last_timestamp_ms // 1000 if last_timestamp_ms is not None else None
            future_limit_s = future_limit_ms // 1000
            if last_timestamp_ms is not None:
                # Filter by timestamp: greater than last timestamp but not more than 15 minutes in the future
                cursor.execute(
                    """
                    SELECT field_config, reported_at
                    FROM health_logs
                    WHERE robot_id = %s
                      AND field_config IS NOT NULL
                      AND reported_at > %s
                      AND reported_at <= %s
                    ORDER BY reported_at DESC
                    LIMIT 1
                    """,
                    (robot_id, last_timestamp_s, future_limit_s),
                )
            else:
                # No last timestamp, just filter by future limit
                cursor.execute(
                    """
                    SELECT field_config, reported_at
                    FROM health_logs
                    WHERE robot_id = %s
                      AND field_config IS NOT NULL
                      AND reported_at <= %s
                    ORDER BY reported_at DESC
                    LIMIT 1
                    """,
                    (robot_id, future_limit_s),
                )

            result = cursor.fetchone()
            if result:
                field_config = json.loads(result[0]) if isinstance(result[0], str) else result[0]
                # Convert reported_at (seconds) to ms for consistency
                timestamp_ms = int(result[1]) * 1000
                return {"field_config": field_config, "timestamp_ms": timestamp_ms}
            return None
    except Exception as e:
        logger.error(f"Failed to get thinning config for robot {robot_id}: {e}")
        return None


def get_thinning_profile(portal_conn: psycopg.Connection, thinning_config_id: str) -> Optional[Dict]:
    """Get thinning profile from portal database."""
    try:
        with portal_conn.cursor() as cursor:
            cursor.execute(
                "SELECT profile FROM profiles WHERE uuid = %s",
                (thinning_config_id,),
            )
            result = cursor.fetchone()
            if result:
                profile = json.loads(result[0]) if isinstance(result[0], str) else result[0]
                return profile
            return None
    except Exception as e:
        logger.error(f"Failed to get thinning profile for {thinning_config_id}: {e}")
        return None


def calculate_thinning_box_sizes(profile: Dict) -> Optional[Dict[str, Dict[str, float]]]:
    """Calculate the thinning box sizes and spacing values for all rows."""
    try:
        rows = profile.get("rows", {})
        if not rows:
            logger.warning("No rows found in thinning profile")
            return None

        thinning_box_data = {}

        # Check all rows in the profile
        for row_key, row_data in rows.items():
            sized_cfg = row_data.get("sizedCfg", {})

            if not sized_cfg:
                logger.warning(f"No sizedCfg found in row {row_key}")
                continue

            max_y_search_radius = sized_cfg.get("maxYSearchRadius")
            min_keepout = sized_cfg.get("minKeepout", {})
            min_keepout_height = min_keepout.get("height")
            ideal_y_dist = sized_cfg.get("idealYDist")

            if max_y_search_radius is None or min_keepout_height is None or ideal_y_dist is None:
                logger.warning(
                    f"Missing required fields in row {row_key}: maxYSearchRadius={max_y_search_radius}, "
                    f"minKeepout.height={min_keepout_height}, idealYDist={ideal_y_dist}"
                )
                continue

            # Calculate spacing values
            min_thinning_spacing = min_keepout_height / 2
            ideal_thinning_spacing = ideal_y_dist
            max_thinning_spacing = max_y_search_radius

            # Calculate thinning box size: max thinning spacing - min thinning spacing
            thinning_box_size = max_thinning_spacing - min_thinning_spacing

            thinning_box_data[row_key] = {
                "min_thinning_spacing": min_thinning_spacing,
                "ideal_thinning_spacing": ideal_thinning_spacing,
                "max_thinning_spacing": max_thinning_spacing,
                "thinning_box_size": thinning_box_size,
            }

        return thinning_box_data if thinning_box_data else None

    except Exception as e:
        logger.error(f"Failed to calculate thinning box sizes: {e}")
        return None


def get_last_thinning_alert(
    alexbot_conn: psycopg.Connection, robot_id: str
) -> Optional[Tuple[str, float, float, float, int]]:
    """Get the last thinning alert data for a robot from alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                "SELECT thinning_profile, max_spacing, ideal_spacing, min_spacing, timestamp_ms "
                "FROM last_thinning_box_alert WHERE robot_id = %s "
                "ORDER BY timestamp_ms DESC LIMIT 1",
                (robot_id,),
            )
            result = cursor.fetchone()
            if result:
                return (result[0], result[1], result[2], result[3], result[4])
            return None
    except Exception as e:
        logger.error(f"Failed to get last thinning alert for robot {robot_id}: {e}")
        return None


def update_last_thinning_alert(
    alexbot_conn: psycopg.Connection,
    robot_id: str,
    thinning_profile: str,
    max_spacing: float,
    ideal_spacing: float,
    min_spacing: float,
    timestamp_ms: int,
):
    """Update the last thinning alert for a robot in alexbotdb."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                INSERT INTO last_thinning_box_alert
                (robot_id, timestamp_ms, thinning_profile, max_spacing, ideal_spacing, min_spacing)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (robot_id) DO UPDATE SET
                    timestamp_ms = EXCLUDED.timestamp_ms,
                    thinning_profile = EXCLUDED.thinning_profile,
                    max_spacing = EXCLUDED.max_spacing,
                    ideal_spacing = EXCLUDED.ideal_spacing,
                    min_spacing = EXCLUDED.min_spacing
                """,
                (robot_id, timestamp_ms, thinning_profile, max_spacing, ideal_spacing, min_spacing),
            )
            alexbot_conn.commit()
            logger.info(f"Updated last thinning alert for robot {robot_id}")
    except Exception as e:
        logger.error(f"Failed to update last thinning alert for robot {robot_id}: {e}")
        alexbot_conn.rollback()


def should_send_thinning_alert(alexbot_conn: psycopg.Connection, robot_id: str, current_timestamp_ms: int) -> bool:
    """Check if enough time has passed since the last thinning alert."""
    try:
        last_alert_data = get_last_thinning_alert(alexbot_conn, robot_id)
        if not last_alert_data:
            return True

        _, _, _, _, last_timestamp_ms = last_alert_data
        time_since_last_alert_ms = current_timestamp_ms - last_timestamp_ms
        alert_interval_ms = THINNING_BOX_ALERT_INTERVAL_MINUTES * 60 * 1000

        return time_since_last_alert_ms >= alert_interval_ms
    except Exception as e:
        logger.error(f"Failed to check thinning alert timing for robot {robot_id}: {e}")
        return True


def send_thinning_box_notification(
    client: WebClient,
    robot_id: str,
    thinning_profile: str,
    thinning_box_data: Dict[str, float],
    threshold: float,
    channel: str,
):
    """Send Slack notification about too big thinning box to the specified channel."""
    try:
        min_spacing = thinning_box_data["min_thinning_spacing"]
        ideal_spacing = thinning_box_data["ideal_thinning_spacing"]
        max_spacing = thinning_box_data["max_thinning_spacing"]
        thinning_box_size = thinning_box_data["thinning_box_size"]

        message = (
            f"📦 *Thinning Box Too Big Alert: {robot_id}* 📦\n"
            f"Profile: *{thinning_profile}*\n"
            f'Min Thinning Spacing: *{min_spacing:.1f}"* | Ideal Thinning Spacing: *{ideal_spacing:.1f}"* | Max Thinning Spacing: *{max_spacing:.1f}"*\n'  # noqa: E501
            f'Thinning Box Size: *{thinning_box_size:.1f}"* (Threshold: *{threshold:.1f}"*)\n\n'
            f"<https://customer.cloud.carbonrobotics.com/fleet/robots/{robot_id}|View in Portal>"
        )

        response = client.chat_postMessage(channel=channel, text=message)
        logger.info(f"Sent thinning box notification for robot {robot_id} to channel {channel}: {response}")
    except Exception as e:
        logger.error(f"Failed to send thinning box notification to channel {channel}: {e}")


def monitor_thinning_box(
    robot_serial: str,
    support_channel: str,
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    slack_client: WebClient,
):
    """Check if a robot's thinning box is too big and send notification if needed."""
    try:
        # Get robot ID from portal database
        robot_id = get_robot_id_from_serial(portal_conn, robot_serial)
        if not robot_id:
            logger.warning(f"Robot with serial {robot_serial} not found in portal database")
            return

        # Get last known thinning alert timestamp
        last_alert_data = get_last_thinning_alert(alexbot_conn, robot_serial)
        last_timestamp_ms = None
        if last_alert_data:
            last_timestamp_ms = last_alert_data[4]

        # Get latest thinning configuration with timestamp filtering
        config_data = get_latest_thinning_config(portal_conn, robot_id, last_timestamp_ms)
        if not config_data:
            logger.warning(f"No new thinning config data found for robot {robot_serial}")
            return

        # Extract thinning information
        field_config = config_data["field_config"]
        is_thinning = field_config.get("is_thinning", False)
        active_thinning_config = field_config.get("active_thinning_config")

        if not is_thinning:
            logger.debug(f"Robot {robot_serial} is not currently thinning")
            return

        if not active_thinning_config:
            logger.warning(f"No active thinning config found for robot {robot_serial}")
            return

        # Get thinning profile
        profile = get_thinning_profile(portal_conn, active_thinning_config)
        if not profile:
            logger.warning(f"Could not get thinning profile for {active_thinning_config}")
            return

        profile_name = profile.get("name", "Unknown Profile")

        # Calculate thinning box sizes for all rows
        thinning_box_data = calculate_thinning_box_sizes(profile)
        if thinning_box_data is None:
            logger.warning(f"Could not calculate thinning box sizes for robot {robot_serial}")
            return

        # Check if any thinning box size is too big
        oversized_rows = []
        max_thinning_box_data = None

        for row_key, row_data in thinning_box_data.items():
            thinning_box_size = row_data["thinning_box_size"]
            if thinning_box_size > MAX_THINNING_BOX_THRESHOLD_INCHES:
                oversized_rows.append((row_key, row_data))
                if max_thinning_box_data is None or thinning_box_size > max_thinning_box_data["thinning_box_size"]:
                    max_thinning_box_data = row_data

        if oversized_rows:
            assert max_thinning_box_data is not None

            # Check if enough time has passed since last alert
            current_timestamp_ms = config_data["timestamp_ms"]
            if should_send_thinning_alert(alexbot_conn, robot_serial, current_timestamp_ms):
                # Send notification
                send_thinning_box_notification(
                    slack_client,
                    robot_serial,
                    profile_name,
                    max_thinning_box_data,
                    MAX_THINNING_BOX_THRESHOLD_INCHES,
                    support_channel,
                )

                # Send visualization
                request_visualization(alexbot_conn, robot_serial, support_channel)

                # Update last alert record
                update_last_thinning_alert(
                    alexbot_conn,
                    robot_serial,
                    profile_name,
                    max_thinning_box_data["max_thinning_spacing"],
                    max_thinning_box_data["ideal_thinning_spacing"],
                    max_thinning_box_data["min_thinning_spacing"],
                    current_timestamp_ms,
                )
            else:
                logger.info(f"Thinning box alert for robot {robot_serial} suppressed due to recent alert")
        else:
            logger.debug(
                f"Robot {robot_serial} all thinning box sizes are within threshold {MAX_THINNING_BOX_THRESHOLD_INCHES}"
            )

    except Exception as e:
        logger.error(f"Error monitoring thinning box for robot {robot_serial}: {e}")
