import logging
from typing import Optional
import psycopg

from utils import get_robot_by_channel

logger = logging.getLogger(__name__)


def validate_robot_serial(serial: str) -> bool:
    """Validate robot serial format (slayer# or reaper#)."""
    if not serial:
        return False

    serial_lower = serial.lower().strip()
    return (serial_lower.startswith("slayer") and serial_lower[6:].isdigit()) or (
        serial_lower.startswith("reaper") and serial_lower[6:].isdigit()
    )


def get_robot_info_from_portal(portal_conn: psycopg.Connection, robot_serial: str) -> Optional[dict]:
    """Get robot information from portal database."""
    try:
        with portal_conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT id, serial, feature_flags, software_version, implementation_status,
                       support_slack, reported_at
                FROM robots
                WHERE LOWER(serial) = LOWER(%s)
                """,
                (robot_serial,),
            )
            result = cursor.fetchone()
            if result:
                return {
                    "id": result[0],
                    "serial": result[1],
                    "feature_flags": result[2],
                    "software_version": result[3],
                    "implementation_status": result[4],
                    "support_slack": result[5],
                    "reported_at": result[6],
                }
            return None
    except Exception as e:
        logger.error(f"Failed to get robot info for {robot_serial}: {e}")
        return None


def format_robot_info_message(robot_info: dict, robot_serial: str, user_name: str) -> str:
    """Format robot information into a Slack message."""
    response_lines = [f"*Robot information for {robot_serial}* (requested by @{user_name})"]

    # Add additional info if available
    if robot_info["software_version"]:
        response_lines.append(f"• Software Version: *{robot_info['software_version']}*")

    # Check embedding-based classification feature
    embedding_enabled = robot_info["feature_flags"].get("embedding_based_classification_feature", False)

    if embedding_enabled:
        classification_method = "Plant Profiles"
    else:
        classification_method = "Thresholds"

    # Build response message
    response_lines.append(f"• Classification Method: *{classification_method}*")

    return "\n".join(response_lines)


def handle_info_command_logic(
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    channel_name: str,
    user_name: str,
    robot_serial: str,
) -> tuple[bool, str, Optional[str]]:

    try:
        # Determine robot serial
        if robot_serial:
            # Robot was specified as argument
            logger.info(f"User @{user_name} requested info for robot {robot_serial} in channel {channel_name}")
        else:
            # No robot specified, determine from channel

            robot_from_channel = get_robot_by_channel(alexbot_conn, channel_name)

            if not robot_from_channel:
                error_msg = (
                    f"❌ Error: No robot configured for channel {channel_name}. "
                    f"Please specify a robot serial: `/info <robot_serial>`"
                )
                return False, error_msg, None

            robot_serial = robot_from_channel

            logger.info(f"User @{user_name} requested info for channel {channel_name} (robot: {robot_serial})")

        # Validate robot serial format
        if not validate_robot_serial(robot_serial):
            error_msg = f"❌ Error: Invalid robot serial format '{robot_serial}'. Expected format: slayer# or reaper#"
            return False, error_msg, None

        # Get robot information from portal database
        robot_info = get_robot_info_from_portal(portal_conn, robot_serial)
        if not robot_info:
            error_msg = f"❌ Error: Robot '{robot_serial}' not found in the system"
            return False, error_msg, None

        # Format the success message
        channel_message = format_robot_info_message(robot_info, robot_serial, user_name)
        return True, channel_message, None

    except Exception as e:
        logger.error(f"Error handling /info command: {e}")
        error_msg = "❌ Error: Failed to process robot info request"
        return False, error_msg, None
