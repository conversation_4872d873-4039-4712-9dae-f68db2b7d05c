# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: weed_tracking/weed_tracking.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'weed_tracking/weed_tracking.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!weed_tracking/weed_tracking.proto\x12\rweed_tracking\"\x07\n\x05\x45mpty\"\xe9\x01\n\nTrajectory\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\ntracker_id\x18\x02 \x01(\r\x12\x0e\n\x06status\x18\x03 \x01(\r\x12\x0f\n\x07is_weed\x18\x04 \x01(\x08\x12\x0c\n\x04x_mm\x18\x05 \x01(\x01\x12\x0c\n\x04y_mm\x18\x06 \x01(\x01\x12\x0c\n\x04z_mm\x18\x07 \x01(\x01\x12%\n\x1dintersected_with_nonshootable\x18\x08 \x01(\x08\x12 \n\x18nonshootable_type_string\x18\t \x01(\t\x12#\n\x1b\x64\x65\x64uplicated_across_tracker\x18\n \x01(\x08:\x02\x18\x01\"\x81\x01\n\x06Target\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x15\n\rtrajectory_id\x18\x02 \x01(\r\x12\x1a\n\x12next_trajectory_id\x18\x03 \x01(\r\x12\x16\n\x0estarting_pos_y\x18\x04 \x01(\x02\x12\x14\n\x0c\x65nding_pos_y\x18\x05 \x01(\x02:\x02\x18\x01\"6\n\x06\x42ounds\x12\x12\n\ntracker_id\x18\x01 \x01(\t\x12\x14\n\x0cmax_pos_mm_y\x18\x02 \x01(\x02:\x02\x18\x01\"\x99\x01\n\x13TrackingStatusReply\x12&\n\x07targets\x18\x01 \x03(\x0b\x32\x15.weed_tracking.Target\x12/\n\x0ctrajectories\x18\x02 \x03(\x0b\x32\x19.weed_tracking.Trajectory\x12%\n\x06\x62ounds\x18\x03 \x03(\x0b\x32\x15.weed_tracking.Bounds:\x02\x18\x01\"K\n\x14GetDetectionsRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\r\n\x05max_x\x18\x03 \x01(\x05\"\xcd\x01\n\tDetection\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\x0c\n\x04size\x18\x03 \x01(\x02\x12\x0b\n\x03\x63lz\x18\x04 \x01(\t\x12\x0f\n\x07is_weed\x18\x05 \x01(\x08\x12\x13\n\x0bout_of_band\x18\x06 \x01(\x08\x12\n\n\x02id\x18\x07 \x01(\r\x12\r\n\x05score\x18\x08 \x01(\x02\x12\x12\n\nweed_score\x18\t \x01(\x02\x12\x12\n\ncrop_score\x18\n \x01(\x02\x12\x11\n\tembedding\x18\x0b \x03(\x02\x12\x13\n\x0bplant_score\x18\x0c \x01(\x02\"P\n\nDetections\x12,\n\ndetections\x18\x01 \x03(\x0b\x32\x18.weed_tracking.Detection\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\",\n\x04\x42\x61nd\x12\x12\n\nstart_x_px\x18\x01 \x01(\x01\x12\x10\n\x08\x65nd_x_px\x18\x02 \x01(\x01\"b\n\x05\x42\x61nds\x12!\n\x04\x62\x61nd\x18\x01 \x03(\x0b\x32\x13.weed_tracking.Band\x12\x17\n\x0f\x62\x61nding_enabled\x18\x02 \x01(\x08\x12\x1d\n\x15row_has_bands_defined\x18\x03 \x01(\x08\"k\n\x15GetDetectionsResponse\x12-\n\ndetections\x18\x01 \x01(\x0b\x32\x19.weed_tracking.Detections\x12#\n\x05\x62\x61nds\x18\x02 \x01(\x0b\x32\x14.weed_tracking.Bands\"\x1e\n\x1cGetTrajectoryMetadataRequest\"A\n\x13TrackedItemMetadata\x12\x14\n\x0c\x64\x65tection_id\x18\x01 \x01(\r\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"\x93\x01\n\x12TrajectoryMetadata\x12\x15\n\rtrajectory_id\x18\x01 \x01(\r\x12\x0e\n\x06\x63\x61m_id\x18\x02 \x01(\t\x12\x41\n\x15tracked_item_metadata\x18\x03 \x03(\x0b\x32\".weed_tracking.TrackedItemMetadata\x12\x13\n\x0b\x62\x61nd_status\x18\x04 \x01(\t\"T\n\x1dGetTrajectoryMetadataResponse\x12\x33\n\x08metadata\x18\x01 \x03(\x0b\x32!.weed_tracking.TrajectoryMetadata\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\r\"\x16\n\tPongReply\x12\t\n\x01x\x18\x01 \x01(\r\"\xa1\x01\n\x0fTrajectoryScore\x12\r\n\x05score\x18\x01 \x01(\x04\x12\x12\n\nscore_tilt\x18\x02 \x01(\x01\x12\x11\n\tscore_pan\x18\x03 \x01(\x01\x12\x12\n\nscore_busy\x18\x04 \x01(\x01\x12\x16\n\x0escore_in_range\x18\x05 \x01(\x01\x12\x18\n\x10score_importance\x18\x06 \x01(\x01\x12\x12\n\nscore_size\x18\x07 \x01(\x01\"4\n\x0fPerScannerScore\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\r\n\x05score\x18\x02 \x01(\x05\"r\n\nScoreState\x12\x34\n\x0ctarget_state\x18\x01 \x01(\x0e\x32\x1e.weed_tracking.TargetableState\x12.\n\x06scores\x18\x02 \x03(\x0b\x32\x1e.weed_tracking.PerScannerScore\"\x1d\n\x05Pos2D\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\"q\n\x07KillBox\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12&\n\x08top_left\x18\x02 \x01(\x0b\x32\x14.weed_tracking.Pos2D\x12*\n\x0c\x62ottom_right\x18\x03 \x01(\x0b\x32\x14.weed_tracking.Pos2D\"\x95\x01\n\nThresholds\x12\x13\n\x07weeding\x18\x01 \x01(\x02\x42\x02\x18\x01\x12\x14\n\x08thinning\x18\x02 \x01(\x02\x42\x02\x18\x01\x12\x13\n\x07\x62\x61nding\x18\x03 \x01(\x02\x42\x02\x18\x01\x12\x16\n\x0epassed_weeding\x18\x04 \x01(\x08\x12\x17\n\x0fpassed_thinning\x18\x05 \x01(\x08\x12\x16\n\x0epassed_banding\x18\x06 \x01(\x08\"\x92\x01\n\tDecisions\x12\x14\n\x0cweeding_weed\x18\x01 \x01(\x08\x12\x14\n\x0cweeding_crop\x18\x02 \x01(\x08\x12\x15\n\rthinning_weed\x18\x03 \x01(\x08\x12\x15\n\rthinning_crop\x18\x04 \x01(\x08\x12\x15\n\rkeepable_crop\x18\x05 \x01(\x08\x12\x14\n\x0c\x62\x61nding_crop\x18\x06 \x01(\x08\"\xe7\x0b\n\x12TrajectorySnapshot\x12\n\n\x02id\x18\x01 \x01(\r\x12.\n\x0bkill_status\x18\x02 \x01(\x0e\x32\x19.weed_tracking.KillStatus\x12\x13\n\x07is_weed\x18\x03 \x01(\x08\x42\x02\x18\x01\x12\x0c\n\x04x_mm\x18\x04 \x01(\x01\x12\x0c\n\x04y_mm\x18\x05 \x01(\x01\x12\x0c\n\x04z_mm\x18\x06 \x01(\x01\x12/\n\x05score\x18\x07 \x01(\x0b\x32\x1e.weed_tracking.TrajectoryScoreH\x00\x12:\n\rinvalid_score\x18\x08 \x01(\x0e\x32!.weed_tracking.InvalidScoreReasonH\x00\x12\x11\n\tradius_mm\x18\t \x01(\x01\x12\x10\n\x08\x63\x61tegory\x18\n \x01(\t\x12\x1f\n\x17shoot_time_requested_ms\x18\x0b \x01(\x04\x12\x1c\n\x14shoot_time_actual_ms\x18\x0c \x01(\x04\x12\x1b\n\x13marked_for_thinning\x18\r \x01(\x08\x12\x12\n\ntracker_id\x18\x0e \x01(\r\x12\x38\n\x10\x64uplicate_status\x18\x0f \x01(\x0e\x32\x1e.weed_tracking.DuplicateStatus\x12\x1f\n\x17\x64uplicate_trajectory_id\x18\x10 \x01(\r\x12\x17\n\x0f\x61ssigned_lasers\x18\x11 \x03(\r\x12\x13\n\x0bout_of_band\x18\x12 \x01(\x08\x12%\n\x1dintersected_with_nonshootable\x18\x13 \x01(\x08\x12R\n\x11\x64\x65tection_classes\x18\x14 \x03(\x0b\x32\x37.weed_tracking.TrajectorySnapshot.DetectionClassesEntry\x12\x12\n\nconfidence\x18\x15 \x01(\x01\x12\x34\n\x0ethinning_state\x18\x16 \x01(\x0e\x32\x1c.weed_tracking.ThinningState\x12\x12\n\nglobal_pos\x18\x17 \x01(\x01\x12.\n\x0bscore_state\x18\x18 \x01(\x0b\x32\x19.weed_tracking.ScoreState\x12\x0b\n\x03\x64oo\x18\x19 \x01(\x02\x12#\n\x1b\x64istance_perspectives_count\x18\x1a \x01(\r\x12\x1b\n\x13size_category_index\x18\x1b \x01(\x05\x12\x1b\n\x13speculative_allowed\x18\x1c \x01(\x08\x12\x19\n\x11protected_by_traj\x18\x1d \x01(\x08\x12-\n\nthresholds\x18\x1e \x01(\x0b\x32\x19.weed_tracking.Thresholds\x12+\n\tdecisions\x18\x1f \x01(\x0b\x32\x18.weed_tracking.Decisions\x12\x12\n\ncrop_score\x18  \x01(\x01\x12\x12\n\nweed_score\x18! \x01(\x01\x12\x13\n\x0bplant_score\x18\" \x01(\x01\x12(\n num_detections_used_for_decision\x18# \x01(\r\x12\x35\n\x0e\x63lassification\x18$ \x01(\x0e\x32\x1d.weed_tracking.Classification\x12V\n\x13\x65mbedding_distances\x18% \x03(\x0b\x32\x39.weed_tracking.TrajectorySnapshot.EmbeddingDistancesEntry\x12(\n speculative_shoot_time_actual_ms\x18& \x01(\r\x12:\n\x11snapshot_metadata\x18\' \x01(\x0b\x32\x1f.weed_tracking.SnapshotMetadata\x1a\x37\n\x15\x44\x65tectionClassesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\x1a\x39\n\x17\x45mbeddingDistancesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\x42\x0f\n\rscore_details\"c\n\x10SnapshotMetadata\x12\x0f\n\x07pcam_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x13\n\x0b\x63\x65nter_x_px\x18\x03 \x01(\x02\x12\x13\n\x0b\x63\x65nter_y_px\x18\x04 \x01(\x02\"A\n\x0e\x42\x61ndDefinition\x12\x11\n\toffset_mm\x18\x01 \x01(\x02\x12\x10\n\x08width_mm\x18\x02 \x01(\x02\x12\n\n\x02id\x18\x03 \x01(\x05\"\x91\x01\n\x14\x43LDAlgorithmSnapshot\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x16\n\x0egraph_points_x\x18\x02 \x03(\x02\x12\x16\n\x0egraph_points_y\x18\x03 \x03(\x02\x12\x11\n\tminimas_x\x18\x04 \x03(\x02\x12\x11\n\tminimas_y\x18\x05 \x03(\x02\x12\r\n\x05lines\x18\x06 \x03(\x02\"\xab\x02\n\x13\x44iagnosticsSnapshot\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x37\n\x0ctrajectories\x18\x02 \x03(\x0b\x32!.weed_tracking.TrajectorySnapshot\x12,\n\x05\x62\x61nds\x18\x03 \x03(\x0b\x32\x1d.weed_tracking.BandDefinition\x12*\n\nkill_boxes\x18\x04 \x03(\x0b\x32\x16.weed_tracking.KillBox\x12L\n\x1a\x62\x61nding_algorithm_snapshot\x18\x05 \x01(\x0b\x32#.weed_tracking.CLDAlgorithmSnapshotH\x00\x88\x01\x01\x42\x1d\n\x1b_banding_algorithm_snapshot\"e\n\x18RecordDiagnosticsRequest\x12\x0f\n\x07ttl_sec\x18\x01 \x01(\r\x12\x1b\n\x13\x63rop_images_per_sec\x18\x02 \x01(\x02\x12\x1b\n\x13weed_images_per_sec\x18\x03 \x01(\x02\"L\n\x1aGetRecordingStatusResponse\x12.\n\x06status\x18\x01 \x01(\x0e\x32\x1e.weed_tracking.RecordingStatus\"M\n)StartSavingCropLineDetectionReplayRequest\x12\x10\n\x08\x66ilename\x18\x01 \x01(\t\x12\x0e\n\x06ttl_ms\x18\x02 \x01(\r\"\xb4\x01\n\x18RecordAimbotInputRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06ttl_ms\x18\x02 \x01(\r\x12\x19\n\x0crotary_ticks\x18\x03 \x01(\x08H\x00\x88\x01\x01\x12\x15\n\x08\x64\x65\x65pweed\x18\x04 \x01(\x08H\x01\x88\x01\x01\x12\x19\n\x0clane_heights\x18\x05 \x01(\x08H\x02\x88\x01\x01\x42\x0f\n\r_rotary_ticksB\x0b\n\t_deepweedB\x0f\n\r_lane_heights\"M\n\x0f\x43onclusionCount\x12+\n\x04type\x18\x01 \x01(\x0e\x32\x1d.weed_tracking.ConclusionType\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"C\n\x11\x43onclusionCounter\x12.\n\x06\x63ounts\x18\x01 \x03(\x0b\x32\x1e.weed_tracking.ConclusionCount\"w\n\x0f\x42\x61ndDefinitions\x12,\n\x05\x62\x61nds\x18\x01 \x03(\x0b\x32\x1d.weed_tracking.BandDefinition\x12\x17\n\x0f\x62\x61nding_enabled\x18\x02 \x01(\x08\x12\x1d\n\x15row_has_bands_defined\x18\x03 \x01(\x08\"\xa2\x02\n\x1aPlantCaptchaStatusResponse\x12\x31\n\x06status\x18\x01 \x01(\x0e\x32!.weed_tracking.PlantCaptchaStatus\x12\x14\n\x0ctotal_images\x18\x02 \x01(\x05\x12\x14\n\x0cimages_taken\x18\x03 \x01(\x05\x12\x16\n\x0emetadata_taken\x18\x04 \x01(\x05\x12V\n\x0f\x65xemplar_counts\x18\x05 \x03(\x0b\x32=.weed_tracking.PlantCaptchaStatusResponse.ExemplarCountsEntry\x1a\x35\n\x13\x45xemplarCountsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\"\x1d\n\tEmbedding\x12\x10\n\x08\x65lements\x18\x01 \x03(\x02\"w\n\x0bWeedClasses\x12\x38\n\x07\x63lasses\x18\x01 \x03(\x0b\x32\'.weed_tracking.WeedClasses.ClassesEntry\x1a.\n\x0c\x43lassesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\xf6\x08\n\x18PlantCaptchaItemMetadata\x12\x12\n\nconfidence\x18\x01 \x01(\x02\x12\x0c\n\x04x_px\x18\x02 \x01(\x05\x12\x0c\n\x04y_px\x18\x03 \x01(\x05\x12\x0c\n\x04x_mm\x18\x04 \x01(\x01\x12\x0c\n\x04y_mm\x18\x05 \x01(\x01\x12\x0c\n\x04z_mm\x18\x06 \x01(\x01\x12\x0f\n\x07size_mm\x18\x07 \x01(\x02\x12K\n\ncategories\x18\x08 \x03(\x0b\x32\x37.weed_tracking.PlantCaptchaItemMetadata.CategoriesEntry\x12\x0b\n\x03\x64oo\x18\t \x01(\x02\x12\x0f\n\x07is_weed\x18\n \x01(\x08\x12%\n\x1dintersected_with_nonshootable\x18\x0b \x01(\x08\x12\x1a\n\x12\x63onfidence_history\x18\x0c \x03(\x02\x12\x12\n\nis_in_band\x18\r \x01(\x08\x12\n\n\x02id\x18\x0e \x01(\t\x12\x0f\n\x07size_px\x18\x0f \x01(\x02\x12\x15\n\rshoot_time_ms\x18\x10 \x01(\r\x12\x1f\n\x17weed_confidence_history\x18\x11 \x03(\x02\x12\x1f\n\x17\x63rop_confidence_history\x18\x12 \x03(\x02\x12\x1b\n\x13size_category_index\x18\x13 \x01(\x05\x12T\n\x0fweed_categories\x18\x14 \x03(\x0b\x32;.weed_tracking.PlantCaptchaItemMetadata.WeedCategoriesEntry\x12\x33\n\x11\x65mbedding_history\x18\x15 \x03(\x0b\x32\x18.weed_tracking.Embedding\x12@\n\rinitial_label\x18\x16 \x01(\x0e\x32).weed_tracking.PlantCaptchaUserPrediction\x12 \n\x18plant_confidence_history\x18\x17 \x03(\x02\x12\x38\n\x14weed_classes_history\x18\x18 \x03(\x0b\x32\x1a.weed_tracking.WeedClasses\x12\x17\n\x0fsize_mm_history\x18\x19 \x03(\x02\x12+\n\tdecisions\x18\x1a \x01(\x0b\x32\x18.weed_tracking.Decisions\x12(\n num_detections_used_for_decision\x18\x1b \x01(\r\x12\\\n\x13\x65mbedding_distances\x18\x1c \x03(\x0b\x32?.weed_tracking.PlantCaptchaItemMetadata.EmbeddingDistancesEntry\x1a\x31\n\x0f\x43\x61tegoriesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\x1a\x35\n\x13WeedCategoriesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\x1a\x39\n\x17\x45mbeddingDistancesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\x1c\n\x1aGetTargetingEnabledRequest\".\n\x1bGetTargetingEnabledResponse\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"\x12\n\x10GetBootedRequest\"#\n\x11GetBootedResponse\x12\x0e\n\x06\x62ooted\x18\x01 \x01(\x08*\xc4\x02\n\x12InvalidScoreReason\x12\x08\n\x04NONE\x10\x00\x12\x1a\n\x16\x41NOTHER_LASER_SHOOTING\x10\x01\x12\x1c\n\x18SCANNER_POSITION_INVALID\x10\x02\x12\x17\n\x13WEED_ALREADY_KILLED\x10\x03\x12\x1e\n\x1aWEED_SHOT_BY_ANOTHER_LASER\x10\x04\x12\x14\n\x10WEED_OUT_OF_BAND\x10\x05\x12\x0e\n\nNOT_A_WEED\x10\x06\x12\x12\n\x0eSCORE_NEGATIVE\x10\x07\x12!\n\x1dINTERSECTED_WITH_NONSHOOTABLE\x10\x08\x12\'\n#EXTERMINATION_FAILURES_EXCEEDED_MAX\x10\t\x12+\n\'DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN\x10\n*\xb0\x01\n\nKillStatus\x12\x13\n\x0fSTATUS_NOT_SHOT\x10\x00\x12\x15\n\x11STATUS_BEING_SHOT\x10\x01\x12\x0f\n\x0bSTATUS_SHOT\x10\x02\x12\x19\n\x15STATUS_PARTIALLY_SHOT\x10\x03\x12\x18\n\x14STATUS_P2P_NOT_FOUND\x10\x04\x12\x10\n\x0cSTATUS_ERROR\x10\x05\x12\x1e\n\x1aSTATUS_P2P_MISSING_CONTEXT\x10\x06*9\n\x0f\x44uplicateStatus\x12\n\n\x06UNIQUE\x10\x00\x12\x0b\n\x07PRIMARY\x10\x01\x12\r\n\tDUPLICATE\x10\x02*n\n\rThinningState\x12\x12\n\x0eTHINNING_UNSET\x10\x00\x12 \n\x1cTHINNING_MARKED_FOR_THINNING\x10\x01\x12\x11\n\rTHINNING_KEPT\x10\x02\x12\x14\n\x10THINNING_IGNORED\x10\x03*\xf4\x01\n\x0fTargetableState\x12\x1b\n\x17TARGET_NOT_IN_SCHEDULER\x10\x00\x12\x11\n\rTARGET_SCORED\x10\x01\x12#\n\x1fTARGET_INTERSECTS_NON_SHOOTABLE\x10\x02\x12\x1c\n\x18TARGET_TOO_MANY_FAILURES\x10\x03\x12\x16\n\x12TARGET_DOO_TOO_LOW\x10\x04\x12\x1f\n\x1bTARGET_IGNORED_FROM_ALMANAC\x10\x05\x12\x16\n\x12TARGET_OUT_OF_BAND\x10\x06\x12\x1d\n\x19TARGET_AVOID_FROM_ALMANAC\x10\x07*h\n\x0e\x43lassification\x12\x13\n\x0f\x43LASS_UNDECIDED\x10\x00\x12\x0e\n\nCLASS_WEED\x10\x01\x12\x0e\n\nCLASS_CROP\x10\x02\x12\x0e\n\nCLASS_BOTH\x10\x03\x12\x11\n\rCLASS_UNKNOWN\x10\x04*i\n\x0fRecordingStatus\x12\x11\n\rNOT_RECORDING\x10\x00\x12\x15\n\x11RECORDING_STARTED\x10\x01\x12\x16\n\x12RECORDING_FINISHED\x10\x02\x12\x14\n\x10RECORDING_FAILED\x10\x03*\x93\x02\n\x0e\x43onclusionType\x12\x0f\n\x0bNOT_WEEDING\x10\x00\x12\x0f\n\x0bOUT_OF_BAND\x10\x01\x12!\n\x1dINTERSECTS_WITH_NON_SHOOTABLE\x10\x02\x12\x10\n\x0cOUT_OF_RANGE\x10\x03\x12\x0f\n\x0bUNIMPORTANT\x10\x04\x12\x0c\n\x08NOT_SHOT\x10\x05\x12\x12\n\x0ePARTIALLY_SHOT\x10\x06\x12\x08\n\x04SHOT\x10\x07\x12\x11\n\rP2P_NOT_FOUND\x10\x08\x12\t\n\x05\x45RROR\x10\t\x12\x0b\n\x07\x46LICKER\x10\n\x12\x17\n\x13MARKED_FOR_THINNING\x10\x0b\x12\x10\n\x0cNOT_TARGETED\x10\x0c\x12\x17\n\x13P2P_MISSING_CONTEXT\x10\r*{\n\x12PlantCaptchaStatus\x12\x0f\n\x0bNOT_STARTED\x10\x00\x12\x13\n\x0f\x43\x41PTCHA_STARTED\x10\x01\x12\x14\n\x10\x43\x41PTCHA_FINISHED\x10\x02\x12\x12\n\x0e\x43\x41PTCHA_FAILED\x10\x03\x12\x15\n\x11\x43\x41PTCHA_CANCELLED\x10\x04*\x7f\n\x1aPlantCaptchaUserPrediction\x12\x08\n\x04WEED\x10\x00\x12\x08\n\x04\x43ROP\x10\x01\x12\x0b\n\x07UNKNOWN\x10\x02\x12\t\n\x05OTHER\x10\x03\x12\n\n\x06IGNORE\x10\x04\x12\r\n\tVOLUNTEER\x10\x05\x12\x0e\n\nBENEFICIAL\x10\x06\x12\n\n\x06\x44\x45\x42RIS\x10\x07\x32\x88\x0c\n\x13WeedTrackingService\x12>\n\x04Ping\x12\x1a.weed_tracking.PingRequest\x1a\x18.weed_tracking.PongReply\"\x00\x12Z\n\rGetDetections\x12#.weed_tracking.GetDetectionsRequest\x1a$.weed_tracking.GetDetectionsResponse\x12r\n\x15GetTrajectoryMetadata\x12+.weed_tracking.GetTrajectoryMetadataRequest\x1a,.weed_tracking.GetTrajectoryMetadataResponse\x12\x39\n\x0bUpdateBands\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12P\n\tGetBooted\x12\x1f.weed_tracking.GetBootedRequest\x1a .weed_tracking.GetBootedResponse\"\x00\x12R\n\x16GetCurrentTrajectories\x12\x14.weed_tracking.Empty\x1a\".weed_tracking.DiagnosticsSnapshot\x12t\n\"StartSavingCropLineDetectionReplay\x12\x38.weed_tracking.StartSavingCropLineDetectionReplayRequest\x1a\x14.weed_tracking.Empty\x12Z\n\x19StartRecordingDiagnostics\x12\'.weed_tracking.RecordDiagnosticsRequest\x1a\x14.weed_tracking.Empty\x12`\n\x1dGetDiagnosticsRecordingStatus\x12\x14.weed_tracking.Empty\x1a).weed_tracking.GetRecordingStatusResponse\x12G\n\x19RemoveRecordingsDirectory\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12[\n\x1aStartRecordingAimbotInputs\x12\'.weed_tracking.RecordAimbotInputRequest\x1a\x14.weed_tracking.Empty\x12N\n\x14GetConclusionCounter\x12\x14.weed_tracking.Empty\x1a .weed_tracking.ConclusionCounter\x12@\n\x08GetBands\x12\x14.weed_tracking.Empty\x1a\x1e.weed_tracking.BandDefinitions\x12?\n\x11StartPlantCaptcha\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12X\n\x15GetPlantCaptchaStatus\x12\x14.weed_tracking.Empty\x1a).weed_tracking.PlantCaptchaStatusResponse\x12I\n\x1bRemovePlantCaptchaDirectory\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12@\n\x12\x43\x61ncelPlantCaptcha\x12\x14.weed_tracking.Empty\x1a\x14.weed_tracking.Empty\x12l\n\x13GetTargetingEnabled\x12).weed_tracking.GetTargetingEnabledRequest\x1a*.weed_tracking.GetTargetingEnabledResponseBGZEgithub.com/carbonrobotics/protos/golang/generated/proto/weed_trackingb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'weed_tracking.weed_tracking_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZEgithub.com/carbonrobotics/protos/golang/generated/proto/weed_tracking'
  _globals['_TRAJECTORY']._loaded_options = None
  _globals['_TRAJECTORY']._serialized_options = b'\030\001'
  _globals['_TARGET']._loaded_options = None
  _globals['_TARGET']._serialized_options = b'\030\001'
  _globals['_BOUNDS']._loaded_options = None
  _globals['_BOUNDS']._serialized_options = b'\030\001'
  _globals['_TRACKINGSTATUSREPLY']._loaded_options = None
  _globals['_TRACKINGSTATUSREPLY']._serialized_options = b'\030\001'
  _globals['_THRESHOLDS'].fields_by_name['weeding']._loaded_options = None
  _globals['_THRESHOLDS'].fields_by_name['weeding']._serialized_options = b'\030\001'
  _globals['_THRESHOLDS'].fields_by_name['thinning']._loaded_options = None
  _globals['_THRESHOLDS'].fields_by_name['thinning']._serialized_options = b'\030\001'
  _globals['_THRESHOLDS'].fields_by_name['banding']._loaded_options = None
  _globals['_THRESHOLDS'].fields_by_name['banding']._serialized_options = b'\030\001'
  _globals['_TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY']._loaded_options = None
  _globals['_TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY']._serialized_options = b'8\001'
  _globals['_TRAJECTORYSNAPSHOT_EMBEDDINGDISTANCESENTRY']._loaded_options = None
  _globals['_TRAJECTORYSNAPSHOT_EMBEDDINGDISTANCESENTRY']._serialized_options = b'8\001'
  _globals['_TRAJECTORYSNAPSHOT'].fields_by_name['is_weed']._loaded_options = None
  _globals['_TRAJECTORYSNAPSHOT'].fields_by_name['is_weed']._serialized_options = b'\030\001'
  _globals['_PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY']._loaded_options = None
  _globals['_PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY']._serialized_options = b'8\001'
  _globals['_WEEDCLASSES_CLASSESENTRY']._loaded_options = None
  _globals['_WEEDCLASSES_CLASSESENTRY']._serialized_options = b'8\001'
  _globals['_PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY']._loaded_options = None
  _globals['_PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY']._serialized_options = b'8\001'
  _globals['_PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY']._loaded_options = None
  _globals['_PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY']._serialized_options = b'8\001'
  _globals['_PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY']._loaded_options = None
  _globals['_PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY']._serialized_options = b'8\001'
  _globals['_INVALIDSCOREREASON']._serialized_start=6999
  _globals['_INVALIDSCOREREASON']._serialized_end=7323
  _globals['_KILLSTATUS']._serialized_start=7326
  _globals['_KILLSTATUS']._serialized_end=7502
  _globals['_DUPLICATESTATUS']._serialized_start=7504
  _globals['_DUPLICATESTATUS']._serialized_end=7561
  _globals['_THINNINGSTATE']._serialized_start=7563
  _globals['_THINNINGSTATE']._serialized_end=7673
  _globals['_TARGETABLESTATE']._serialized_start=7676
  _globals['_TARGETABLESTATE']._serialized_end=7920
  _globals['_CLASSIFICATION']._serialized_start=7922
  _globals['_CLASSIFICATION']._serialized_end=8026
  _globals['_RECORDINGSTATUS']._serialized_start=8028
  _globals['_RECORDINGSTATUS']._serialized_end=8133
  _globals['_CONCLUSIONTYPE']._serialized_start=8136
  _globals['_CONCLUSIONTYPE']._serialized_end=8411
  _globals['_PLANTCAPTCHASTATUS']._serialized_start=8413
  _globals['_PLANTCAPTCHASTATUS']._serialized_end=8536
  _globals['_PLANTCAPTCHAUSERPREDICTION']._serialized_start=8538
  _globals['_PLANTCAPTCHAUSERPREDICTION']._serialized_end=8665
  _globals['_EMPTY']._serialized_start=52
  _globals['_EMPTY']._serialized_end=59
  _globals['_TRAJECTORY']._serialized_start=62
  _globals['_TRAJECTORY']._serialized_end=295
  _globals['_TARGET']._serialized_start=298
  _globals['_TARGET']._serialized_end=427
  _globals['_BOUNDS']._serialized_start=429
  _globals['_BOUNDS']._serialized_end=483
  _globals['_TRACKINGSTATUSREPLY']._serialized_start=486
  _globals['_TRACKINGSTATUSREPLY']._serialized_end=639
  _globals['_GETDETECTIONSREQUEST']._serialized_start=641
  _globals['_GETDETECTIONSREQUEST']._serialized_end=716
  _globals['_DETECTION']._serialized_start=719
  _globals['_DETECTION']._serialized_end=924
  _globals['_DETECTIONS']._serialized_start=926
  _globals['_DETECTIONS']._serialized_end=1006
  _globals['_BAND']._serialized_start=1008
  _globals['_BAND']._serialized_end=1052
  _globals['_BANDS']._serialized_start=1054
  _globals['_BANDS']._serialized_end=1152
  _globals['_GETDETECTIONSRESPONSE']._serialized_start=1154
  _globals['_GETDETECTIONSRESPONSE']._serialized_end=1261
  _globals['_GETTRAJECTORYMETADATAREQUEST']._serialized_start=1263
  _globals['_GETTRAJECTORYMETADATAREQUEST']._serialized_end=1293
  _globals['_TRACKEDITEMMETADATA']._serialized_start=1295
  _globals['_TRACKEDITEMMETADATA']._serialized_end=1360
  _globals['_TRAJECTORYMETADATA']._serialized_start=1363
  _globals['_TRAJECTORYMETADATA']._serialized_end=1510
  _globals['_GETTRAJECTORYMETADATARESPONSE']._serialized_start=1512
  _globals['_GETTRAJECTORYMETADATARESPONSE']._serialized_end=1596
  _globals['_PINGREQUEST']._serialized_start=1598
  _globals['_PINGREQUEST']._serialized_end=1622
  _globals['_PONGREPLY']._serialized_start=1624
  _globals['_PONGREPLY']._serialized_end=1646
  _globals['_TRAJECTORYSCORE']._serialized_start=1649
  _globals['_TRAJECTORYSCORE']._serialized_end=1810
  _globals['_PERSCANNERSCORE']._serialized_start=1812
  _globals['_PERSCANNERSCORE']._serialized_end=1864
  _globals['_SCORESTATE']._serialized_start=1866
  _globals['_SCORESTATE']._serialized_end=1980
  _globals['_POS2D']._serialized_start=1982
  _globals['_POS2D']._serialized_end=2011
  _globals['_KILLBOX']._serialized_start=2013
  _globals['_KILLBOX']._serialized_end=2126
  _globals['_THRESHOLDS']._serialized_start=2129
  _globals['_THRESHOLDS']._serialized_end=2278
  _globals['_DECISIONS']._serialized_start=2281
  _globals['_DECISIONS']._serialized_end=2427
  _globals['_TRAJECTORYSNAPSHOT']._serialized_start=2430
  _globals['_TRAJECTORYSNAPSHOT']._serialized_end=3941
  _globals['_TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY']._serialized_start=3810
  _globals['_TRAJECTORYSNAPSHOT_DETECTIONCLASSESENTRY']._serialized_end=3865
  _globals['_TRAJECTORYSNAPSHOT_EMBEDDINGDISTANCESENTRY']._serialized_start=3867
  _globals['_TRAJECTORYSNAPSHOT_EMBEDDINGDISTANCESENTRY']._serialized_end=3924
  _globals['_SNAPSHOTMETADATA']._serialized_start=3943
  _globals['_SNAPSHOTMETADATA']._serialized_end=4042
  _globals['_BANDDEFINITION']._serialized_start=4044
  _globals['_BANDDEFINITION']._serialized_end=4109
  _globals['_CLDALGORITHMSNAPSHOT']._serialized_start=4112
  _globals['_CLDALGORITHMSNAPSHOT']._serialized_end=4257
  _globals['_DIAGNOSTICSSNAPSHOT']._serialized_start=4260
  _globals['_DIAGNOSTICSSNAPSHOT']._serialized_end=4559
  _globals['_RECORDDIAGNOSTICSREQUEST']._serialized_start=4561
  _globals['_RECORDDIAGNOSTICSREQUEST']._serialized_end=4662
  _globals['_GETRECORDINGSTATUSRESPONSE']._serialized_start=4664
  _globals['_GETRECORDINGSTATUSRESPONSE']._serialized_end=4740
  _globals['_STARTSAVINGCROPLINEDETECTIONREPLAYREQUEST']._serialized_start=4742
  _globals['_STARTSAVINGCROPLINEDETECTIONREPLAYREQUEST']._serialized_end=4819
  _globals['_RECORDAIMBOTINPUTREQUEST']._serialized_start=4822
  _globals['_RECORDAIMBOTINPUTREQUEST']._serialized_end=5002
  _globals['_CONCLUSIONCOUNT']._serialized_start=5004
  _globals['_CONCLUSIONCOUNT']._serialized_end=5081
  _globals['_CONCLUSIONCOUNTER']._serialized_start=5083
  _globals['_CONCLUSIONCOUNTER']._serialized_end=5150
  _globals['_BANDDEFINITIONS']._serialized_start=5152
  _globals['_BANDDEFINITIONS']._serialized_end=5271
  _globals['_PLANTCAPTCHASTATUSRESPONSE']._serialized_start=5274
  _globals['_PLANTCAPTCHASTATUSRESPONSE']._serialized_end=5564
  _globals['_PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY']._serialized_start=5511
  _globals['_PLANTCAPTCHASTATUSRESPONSE_EXEMPLARCOUNTSENTRY']._serialized_end=5564
  _globals['_EMBEDDING']._serialized_start=5566
  _globals['_EMBEDDING']._serialized_end=5595
  _globals['_WEEDCLASSES']._serialized_start=5597
  _globals['_WEEDCLASSES']._serialized_end=5716
  _globals['_WEEDCLASSES_CLASSESENTRY']._serialized_start=5670
  _globals['_WEEDCLASSES_CLASSESENTRY']._serialized_end=5716
  _globals['_PLANTCAPTCHAITEMMETADATA']._serialized_start=5719
  _globals['_PLANTCAPTCHAITEMMETADATA']._serialized_end=6861
  _globals['_PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY']._serialized_start=6698
  _globals['_PLANTCAPTCHAITEMMETADATA_CATEGORIESENTRY']._serialized_end=6747
  _globals['_PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY']._serialized_start=6749
  _globals['_PLANTCAPTCHAITEMMETADATA_WEEDCATEGORIESENTRY']._serialized_end=6802
  _globals['_PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY']._serialized_start=3867
  _globals['_PLANTCAPTCHAITEMMETADATA_EMBEDDINGDISTANCESENTRY']._serialized_end=3924
  _globals['_GETTARGETINGENABLEDREQUEST']._serialized_start=6863
  _globals['_GETTARGETINGENABLEDREQUEST']._serialized_end=6891
  _globals['_GETTARGETINGENABLEDRESPONSE']._serialized_start=6893
  _globals['_GETTARGETINGENABLEDRESPONSE']._serialized_end=6939
  _globals['_GETBOOTEDREQUEST']._serialized_start=6941
  _globals['_GETBOOTEDREQUEST']._serialized_end=6959
  _globals['_GETBOOTEDRESPONSE']._serialized_start=6961
  _globals['_GETBOOTEDRESPONSE']._serialized_end=6996
  _globals['_WEEDTRACKINGSERVICE']._serialized_start=8668
  _globals['_WEEDTRACKINGSERVICE']._serialized_end=10212
# @@protoc_insertion_point(module_scope)
