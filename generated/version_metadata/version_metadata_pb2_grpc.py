# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.util import util_pb2 as util_dot_util__pb2
from generated.version_metadata import version_metadata_pb2 as version__metadata_dot_version__metadata__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in version_metadata/version_metadata_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class VersionMetadataServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetVersionMetadata = channel.unary_unary(
                '/carbon.version_metadata.VersionMetadataService/GetVersionMetadata',
                request_serializer=version__metadata_dot_version__metadata__pb2.GetVersionMetadataRequest.SerializeToString,
                response_deserializer=version__metadata_dot_version__metadata__pb2.VersionMetadata.FromString,
                _registered_method=True)
        self.UploadVersionMetadata = channel.unary_unary(
                '/carbon.version_metadata.VersionMetadataService/UploadVersionMetadata',
                request_serializer=version__metadata_dot_version__metadata__pb2.UploadVersionMetadataRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class VersionMetadataServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetVersionMetadata(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UploadVersionMetadata(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_VersionMetadataServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetVersionMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.GetVersionMetadata,
                    request_deserializer=version__metadata_dot_version__metadata__pb2.GetVersionMetadataRequest.FromString,
                    response_serializer=version__metadata_dot_version__metadata__pb2.VersionMetadata.SerializeToString,
            ),
            'UploadVersionMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadVersionMetadata,
                    request_deserializer=version__metadata_dot_version__metadata__pb2.UploadVersionMetadataRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.version_metadata.VersionMetadataService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.version_metadata.VersionMetadataService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class VersionMetadataService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetVersionMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.version_metadata.VersionMetadataService/GetVersionMetadata',
            version__metadata_dot_version__metadata__pb2.GetVersionMetadataRequest.SerializeToString,
            version__metadata_dot_version__metadata__pb2.VersionMetadata.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UploadVersionMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.version_metadata.VersionMetadataService/UploadVersionMetadata',
            version__metadata_dot_version__metadata__pb2.UploadVersionMetadataRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
