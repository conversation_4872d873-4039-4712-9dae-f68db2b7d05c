# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: version_metadata/version_metadata.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'version_metadata/version_metadata.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'version_metadata/version_metadata.proto\x12\x17\x63\x61rbon.version_metadata\x1a\x0futil/util.proto\"<\n\x0fVersionMetadata\x12\x12\n\ncontainers\x18\x01 \x03(\t\x12\x15\n\rsystemVersion\x18\x02 \x01(\t\"9\n\x19GetVersionMetadataRequest\x12\x0f\n\x07version\x18\x01 \x01(\t\x12\x0b\n\x03gen\x18\x02 \x01(\t\"\x7f\n\x1cUploadVersionMetadataRequest\x12\x0f\n\x07version\x18\x01 \x01(\t\x12\x0b\n\x03gen\x18\x02 \x01(\t\x12\x41\n\x0fversionMetadata\x18\x03 \x01(\x0b\x32(.carbon.version_metadata.VersionMetadata2\xf0\x01\n\x16VersionMetadataService\x12r\n\x12GetVersionMetadata\x12\x32.carbon.version_metadata.GetVersionMetadataRequest\x1a(.carbon.version_metadata.VersionMetadata\x12\x62\n\x15UploadVersionMetadata\x12\x35.carbon.version_metadata.UploadVersionMetadataRequest\x1a\x12.carbon.util.EmptyBJZHgithub.com/carbonrobotics/protos/golang/generated/proto/version_metadatab\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'version_metadata.version_metadata_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZHgithub.com/carbonrobotics/protos/golang/generated/proto/version_metadata'
  _globals['_VERSIONMETADATA']._serialized_start=85
  _globals['_VERSIONMETADATA']._serialized_end=145
  _globals['_GETVERSIONMETADATAREQUEST']._serialized_start=147
  _globals['_GETVERSIONMETADATAREQUEST']._serialized_end=204
  _globals['_UPLOADVERSIONMETADATAREQUEST']._serialized_start=206
  _globals['_UPLOADVERSIONMETADATAREQUEST']._serialized_end=333
  _globals['_VERSIONMETADATASERVICE']._serialized_start=336
  _globals['_VERSIONMETADATASERVICE']._serialized_end=576
# @@protoc_insertion_point(module_scope)
