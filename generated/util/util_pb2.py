# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: util/util.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'util/util.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0futil/util.proto\x12\x0b\x63\x61rbon.util\"\x07\n\x05\x45mpty\"!\n\tTimestamp\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\"\x95\x01\n\x0c\x46\x65\x61tureFlags\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x33\n\x05\x66lags\x18\x02 \x03(\x0b\x32$.carbon.util.FeatureFlags.FlagsEntry\x1a,\n\nFlagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\x42>Z<github.com/carbonrobotics/protos/golang/generated/proto/utilb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'util.util_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z<github.com/carbonrobotics/protos/golang/generated/proto/util'
  _globals['_FEATUREFLAGS_FLAGSENTRY']._loaded_options = None
  _globals['_FEATUREFLAGS_FLAGSENTRY']._serialized_options = b'8\001'
  _globals['_EMPTY']._serialized_start=32
  _globals['_EMPTY']._serialized_end=39
  _globals['_TIMESTAMP']._serialized_start=41
  _globals['_TIMESTAMP']._serialized_end=74
  _globals['_FEATUREFLAGS']._serialized_start=77
  _globals['_FEATUREFLAGS']._serialized_end=226
  _globals['_FEATUREFLAGS_FLAGSENTRY']._serialized_start=182
  _globals['_FEATUREFLAGS_FLAGSENTRY']._serialized_end=226
# @@protoc_insertion_point(module_scope)
