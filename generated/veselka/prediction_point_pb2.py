# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: veselka/prediction_point.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'veselka/prediction_point.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.veselka import pagination_pb2 as veselka_dot_pagination__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1eveselka/prediction_point.proto\x12\x1f\x63\x61rbon.veselka.prediction_point\x1a\x18veselka/pagination.proto\"C\n\x05Image\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\x0c\n\x04ppcm\x18\x03 \x01(\x02\x12\x13\n\x0b\x63\x61ptured_at\x18\x04 \x01(\x03\"\x8f\x01\n\x0fPredictionPoint\x12\n\n\x02id\x18\x01 \x01(\t\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\x12\x0e\n\x06radius\x18\x04 \x01(\x02\x12\x35\n\x05image\x18\x05 \x01(\x0b\x32&.carbon.veselka.prediction_point.Image\x12\x13\n\x0b\x63\x61tegory_id\x18\x06 \x01(\t\"\x99\x01\n\x1cListPredictionPointsResponse\x12>\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x30.carbon.veselka.prediction_point.PredictionPoint\x12\x39\n\npagination\x18\x02 \x01(\x0b\x32%.carbon.veselka.pagination.Pagination\";\n\x0f\x43\x61tegoryProfile\x12\n\n\x02id\x18\x01 \x01(\t\x12\x1c\n\x14prediction_point_ids\x18\x02 \x03(\t\"t\n\x19\x43\x61tegoryCollectionProfile\x12\n\n\x02id\x18\x01 \x01(\t\x12K\n\x11\x63\x61tegory_profiles\x18\x02 \x03(\x0b\x32\x30.carbon.veselka.prediction_point.CategoryProfile\"\xab\x01\n#CreatePredictionPointSessionRequest\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x11\n\trobot_ids\x18\x02 \x03(\t\x12_\n\x1b\x63\x61tegory_collection_profile\x18\x03 \x01(\x0b\x32:.carbon.veselka.prediction_point.CategoryCollectionProfile\"\x81\x01\n$CreatePredictionPointSessionResponse\x12\x12\n\nsession_id\x18\x01 \x01(\t\x12\x45\n\x06status\x18\x02 \x01(\x0e\x32\x35.carbon.veselka.prediction_point.SessionActivityState\"\xa7\x01\n\x0bSessionInfo\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x11\n\trobot_ids\x18\x02 \x03(\t\x12_\n\x1b\x63\x61tegory_collection_profile\x18\x03 \x01(\x0b\x32:.carbon.veselka.prediction_point.CategoryCollectionProfile\x12\x12\n\nsession_id\x18\x04 \x01(\t\"\x90\x01\n\rSessionStatus\x12\x18\n\x10\x63ount_of_results\x18\x01 \x01(\x03\x12\x16\n\x0e\x65xpected_count\x18\x02 \x01(\x03\x12M\n\x0esession_status\x18\x03 \x01(\x0e\x32\x35.carbon.veselka.prediction_point.SessionActivityState\"\xaf\x01\n!GetPredictionPointSessionResponse\x12\x42\n\x0csession_info\x18\x01 \x01(\x0b\x32,.carbon.veselka.prediction_point.SessionInfo\x12\x46\n\x0esession_status\x18\x02 \x01(\x0b\x32..carbon.veselka.prediction_point.SessionStatus*X\n\x14SessionActivityState\x12&\n\"SESSION_ACTIVITY_STATE_UNSPECIFIED\x10\x00\x12\n\n\x06\x41\x43TIVE\x10\x01\x12\x0c\n\x08INACTIVE\x10\x02\x42RZPgithub.com/carbonrobotics/protos/golang/generated/proto/veselka/prediction_pointb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'veselka.prediction_point_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZPgithub.com/carbonrobotics/protos/golang/generated/proto/veselka/prediction_point'
  _globals['_SESSIONACTIVITYSTATE']._serialized_start=1444
  _globals['_SESSIONACTIVITYSTATE']._serialized_end=1532
  _globals['_IMAGE']._serialized_start=93
  _globals['_IMAGE']._serialized_end=160
  _globals['_PREDICTIONPOINT']._serialized_start=163
  _globals['_PREDICTIONPOINT']._serialized_end=306
  _globals['_LISTPREDICTIONPOINTSRESPONSE']._serialized_start=309
  _globals['_LISTPREDICTIONPOINTSRESPONSE']._serialized_end=462
  _globals['_CATEGORYPROFILE']._serialized_start=464
  _globals['_CATEGORYPROFILE']._serialized_end=523
  _globals['_CATEGORYCOLLECTIONPROFILE']._serialized_start=525
  _globals['_CATEGORYCOLLECTIONPROFILE']._serialized_end=641
  _globals['_CREATEPREDICTIONPOINTSESSIONREQUEST']._serialized_start=644
  _globals['_CREATEPREDICTIONPOINTSESSIONREQUEST']._serialized_end=815
  _globals['_CREATEPREDICTIONPOINTSESSIONRESPONSE']._serialized_start=818
  _globals['_CREATEPREDICTIONPOINTSESSIONRESPONSE']._serialized_end=947
  _globals['_SESSIONINFO']._serialized_start=950
  _globals['_SESSIONINFO']._serialized_end=1117
  _globals['_SESSIONSTATUS']._serialized_start=1120
  _globals['_SESSIONSTATUS']._serialized_end=1264
  _globals['_GETPREDICTIONPOINTSESSIONRESPONSE']._serialized_start=1267
  _globals['_GETPREDICTIONPOINTSESSIONRESPONSE']._serialized_end=1442
# @@protoc_insertion_point(module_scope)
