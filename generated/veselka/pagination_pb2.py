# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: veselka/pagination.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'veselka/pagination.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18veselka/pagination.proto\x12\x19\x63\x61rbon.veselka.pagination\"\x8f\x01\n\nPagination\x12\x15\n\rtotal_records\x18\x01 \x01(\x05\x12?\n\ncount_type\x18\x02 \x01(\x0e\x32+.carbon.veselka.pagination.ResultsCountType\x12\x14\n\x0c\x63urrent_page\x18\x03 \x01(\x05\x12\x13\n\x0btotal_pages\x18\x04 \x01(\x05*;\n\x10ResultsCountType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0b\n\x07PRECISE\x10\x01\x12\r\n\tESTIMATED\x10\x02\x42LZJgithub.com/carbonrobotics/protos/golang/generated/proto/veselka/paginationb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'veselka.pagination_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZJgithub.com/carbonrobotics/protos/golang/generated/proto/veselka/pagination'
  _globals['_RESULTSCOUNTTYPE']._serialized_start=201
  _globals['_RESULTSCOUNTTYPE']._serialized_end=260
  _globals['_PAGINATION']._serialized_start=56
  _globals['_PAGINATION']._serialized_end=199
# @@protoc_insertion_point(module_scope)
