# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.veselka.cv import veselka_cv_pb2 as veselka_dot_cv_dot_veselka__cv__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in veselka/cv/veselka_cv_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class DLPredictStub(object):
    """Service Definition 
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.DLPrediction = channel.unary_unary(
                '/DLPredict/DLPrediction',
                request_serializer=veselka_dot_cv_dot_veselka__cv__pb2.DLPredictionRequest.SerializeToString,
                response_deserializer=veselka_dot_cv_dot_veselka__cv__pb2.DLPredictionResponse.FromString,
                _registered_method=True)


class DLPredictServicer(object):
    """Service Definition 
    """

    def DLPrediction(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DLPredictServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'DLPrediction': grpc.unary_unary_rpc_method_handler(
                    servicer.DLPrediction,
                    request_deserializer=veselka_dot_cv_dot_veselka__cv__pb2.DLPredictionRequest.FromString,
                    response_serializer=veselka_dot_cv_dot_veselka__cv__pb2.DLPredictionResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'DLPredict', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('DLPredict', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class DLPredict(object):
    """Service Definition 
    """

    @staticmethod
    def DLPrediction(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/DLPredict/DLPrediction',
            veselka_dot_cv_dot_veselka__cv__pb2.DLPredictionRequest.SerializeToString,
            veselka_dot_cv_dot_veselka__cv__pb2.DLPredictionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
