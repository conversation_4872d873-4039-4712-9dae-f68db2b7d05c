# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: veselka/cv/veselka_cv.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'veselka/cv/veselka_cv.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1bveselka/cv/veselka_cv.proto\"%\n\x04Size\x12\r\n\x05width\x18\x01 \x01(\x05\x12\x0e\n\x06height\x18\x02 \x01(\x05\"o\n\x04Mask\x12\x13\n\x04size\x18\x01 \x01(\x0b\x32\x05.Size\x12\x0f\n\x07s3_path\x18\x02 \x01(\t\x12\x0b\n\x03ppi\x18\x03 \x01(\x05\x12\x11\n\x04\x64\x61ta\x18\x04 \x01(\x0cH\x00\x88\x01\x01\x12\x10\n\x03\x63ls\x18\x05 \x01(\tH\x01\x88\x01\x01\x42\x07\n\x05_dataB\x06\n\x04_cls\":\n\x05Image\x12\x13\n\x04size\x18\x01 \x01(\x0b\x32\x05.Size\x12\x0f\n\x07s3_path\x18\x02 \x01(\t\x12\x0b\n\x03ppi\x18\x03 \x01(\x05\"\'\n\tDetection\x12\x0b\n\x03\x63ls\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\x02\"i\n\x05Point\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\x0e\n\x06radius\x18\x03 \x01(\x02\x12\r\n\x05score\x18\x04 \x01(\x02\x12\x0b\n\x03\x63ls\x18\x05 \x01(\t\x12\x1e\n\ndetections\x18\x06 \x03(\x0b\x32\n.Detection\"\xab\x01\n\x13\x44LPredictionRequest\x12\x15\n\x05image\x18\x01 \x01(\x0b\x32\x06.Image\x12\x10\n\x08model_id\x18\x02 \x01(\t\x12\x1b\n\x0fmodel_threshold\x18\x03 \x01(\x02\x42\x02\x18\x01\x12\x16\n\x0e\x63rop_threshold\x18\x04 \x01(\x02\x12\x16\n\x0eweed_threshold\x18\x05 \x01(\x02\x12\x1e\n\x16segmentation_threshold\x18\x06 \x01(\x02\"u\n\x14\x44LPredictionResponse\x12\x16\n\x06points\x18\x01 \x03(\x0b\x32\x06.Point\x12\x13\n\x04mask\x18\x02 \x01(\x0b\x32\x05.Mask\x12\x14\n\x05masks\x18\x03 \x03(\x0b\x32\x05.Mask\x12\x11\n\x04hack\x18\x04 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_hack2J\n\tDLPredict\x12=\n\x0c\x44LPrediction\x12\x14.DLPredictionRequest\x1a\x15.DLPredictionResponse\"\x00\x42OZMgithub.com/carbonrobotics/protos/golang/generated/proto/veselka/cv/veselka_cvb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'veselka.cv.veselka_cv_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZMgithub.com/carbonrobotics/protos/golang/generated/proto/veselka/cv/veselka_cv'
  _globals['_DLPREDICTIONREQUEST'].fields_by_name['model_threshold']._loaded_options = None
  _globals['_DLPREDICTIONREQUEST'].fields_by_name['model_threshold']._serialized_options = b'\030\001'
  _globals['_SIZE']._serialized_start=31
  _globals['_SIZE']._serialized_end=68
  _globals['_MASK']._serialized_start=70
  _globals['_MASK']._serialized_end=181
  _globals['_IMAGE']._serialized_start=183
  _globals['_IMAGE']._serialized_end=241
  _globals['_DETECTION']._serialized_start=243
  _globals['_DETECTION']._serialized_end=282
  _globals['_POINT']._serialized_start=284
  _globals['_POINT']._serialized_end=389
  _globals['_DLPREDICTIONREQUEST']._serialized_start=392
  _globals['_DLPREDICTIONREQUEST']._serialized_end=563
  _globals['_DLPREDICTIONRESPONSE']._serialized_start=565
  _globals['_DLPREDICTIONRESPONSE']._serialized_end=682
  _globals['_DLPREDICT']._serialized_start=684
  _globals['_DLPREDICT']._serialized_end=758
# @@protoc_insertion_point(module_scope)
